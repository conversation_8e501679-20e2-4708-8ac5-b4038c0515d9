#!/usr/bin/env node

/**
 * Test script for Google Apps Script Calendar Endpoint
 * This helps debug the calendar integration issues
 */

const CALENDAR_ENDPOINT = "https://script.google.com/macros/s/AKfycbzmTmeMHbXbDwoB5Odz5E93L-ct5jRlXVdsJbxZ7Xlg6XIm1v8vC9Muy2pycQErj_np/exec";

async function testCalendarEndpoint() {
  console.log('🧪 Testing Google Apps Script Calendar Endpoint\n');
  console.log(`Endpoint: ${CALENDAR_ENDPOINT}\n`);

  // Test 1: OPTIONS request (CORS preflight)
  console.log('📋 Test 1: OPTIONS Request (CORS)');
  console.log('─'.repeat(50));
  
  try {
    const optionsResponse = await fetch(CALENDAR_ENDPOINT, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://elgatoai.com',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    console.log(`Status: ${optionsResponse.status} ${optionsResponse.statusText}`);
    console.log('CORS Headers:');
    console.log(`  Access-Control-Allow-Origin: ${optionsResponse.headers.get('Access-Control-Allow-Origin')}`);
    console.log(`  Access-Control-Allow-Methods: ${optionsResponse.headers.get('Access-Control-Allow-Methods')}`);
    console.log(`  Access-Control-Allow-Headers: ${optionsResponse.headers.get('Access-Control-Allow-Headers')}`);
    
    if (optionsResponse.status === 200) {
      console.log('✅ CORS preflight successful');
    } else {
      console.log('❌ CORS preflight failed');
    }
  } catch (error) {
    console.log('❌ OPTIONS request failed:', error.message);
  }

  // Test 2: Simple POST request
  console.log('\n📋 Test 2: Simple POST Request');
  console.log('─'.repeat(50));
  
  const testData = {
    calendar: '<EMAIL>',
    message: 'Test booking from debug script',
    userEmail: '<EMAIL>',
    site: 'odmustafa'
  };
  
  try {
    console.log('Sending request with data:', JSON.stringify(testData, null, 2));
    
    const response = await fetch(CALENDAR_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'https://elgatoai.com'
      },
      body: JSON.stringify(testData)
    });
    
    console.log(`\nStatus: ${response.status} ${response.statusText}`);
    console.log('Response Headers:');
    for (const [key, value] of response.headers.entries()) {
      console.log(`  ${key}: ${value}`);
    }
    
    const contentType = response.headers.get('content-type');
    console.log(`\nContent-Type: ${contentType}`);
    
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      console.log('\n✅ JSON Response:');
      console.log(JSON.stringify(data, null, 2));
    } else {
      const text = await response.text();
      console.log('\n❌ Non-JSON Response (first 500 chars):');
      console.log(text.substring(0, 500));
      
      if (text.includes('<!DOCTYPE')) {
        console.log('\n🚨 ISSUE DETECTED: Receiving HTML instead of JSON');
        console.log('This usually means:');
        console.log('1. Web app not properly deployed');
        console.log('2. Permissions not granted');
        console.log('3. Script has syntax errors');
        console.log('\nSee GOOGLE-APPS-SCRIPT-TROUBLESHOOTING.md for solutions');
      }
    }
    
  } catch (error) {
    console.log('❌ POST request failed:', error.message);
  }

  // Test 3: Minimal data test
  console.log('\n📋 Test 3: Minimal Data Test');
  console.log('─'.repeat(50));
  
  const minimalData = {
    calendar: '<EMAIL>',
    message: 'minimal test'
  };
  
  try {
    console.log('Sending minimal data:', JSON.stringify(minimalData, null, 2));
    
    const response = await fetch(CALENDAR_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(minimalData)
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      console.log('✅ Minimal test successful:', data);
    } else {
      const text = await response.text();
      console.log('❌ Minimal test failed - HTML response received');
    }
    
  } catch (error) {
    console.log('❌ Minimal test failed:', error.message);
  }

  // Summary
  console.log('\n🎯 Summary & Next Steps');
  console.log('─'.repeat(50));
  console.log('If you see HTML responses instead of JSON:');
  console.log('1. Redeploy the Google Apps Script as a web app');
  console.log('2. Set "Who has access" to "Anyone"');
  console.log('3. Grant all requested permissions');
  console.log('4. Use the new deployment URL');
  console.log('\nFor detailed instructions, see: GOOGLE-APPS-SCRIPT-TROUBLESHOOTING.md');
}

// Helper function to check if running in Node.js
if (typeof window === 'undefined') {
  // Running in Node.js
  testCalendarEndpoint().catch(console.error);
} else {
  // Running in browser
  console.log('Run this script in Node.js or copy the functions to browser console');
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testCalendarEndpoint, CALENDAR_ENDPOINT };
}
