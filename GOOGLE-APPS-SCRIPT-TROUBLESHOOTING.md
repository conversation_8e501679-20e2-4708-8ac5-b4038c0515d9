# Google Apps Script Calendar Integration - Troubleshooting

## 🚨 Current Issue
The calendar API endpoint is returning HTML instead of JSON, which indicates a deployment or permission issue.

**Error:** `Unexpected token '<', "<!DOCTYPE "... is not valid JSON`

## 🔧 Step-by-Step Fix

### 1. Verify Google Apps Script Deployment

1. **Go to [Google Apps Script](https://script.google.com/)**
2. **Open your project** (or create a new one if needed)
3. **Paste the code** from `google-apps-script-calendar.gs`
4. **Save the project** (Ctrl+S or Cmd+S)

### 2. Deploy as Web App (Critical Steps)

1. **Click "Deploy" → "New deployment"**
2. **Choose "Web app" as the type**
3. **Set the following configuration:**
   - **Execute as:** "Me (<EMAIL>)"
   - **Who has access:** "Anyone" (This is crucial!)
4. **Click "Deploy"**
5. **Copy the Web App URL** (should look like your current URL)

### 3. Grant Permissions

1. **After deployment, you'll see a permissions dialog**
2. **Click "Review permissions"**
3. **Choose your Google account**
4. **Click "Advanced" if you see a warning**
5. **Click "Go to [Project Name] (unsafe)"**
6. **Click "Allow"**

### 4. Test the Deployment

Use this curl command to test:

```bash
curl -X POST "https://script.google.com/macros/s/AKfycbzmTmeMHbXbDwoB5Odz5E93L-ct5jRlXVdsJbxZ7Xlg6XIm1v8vC9Muy2pycQErj_np/exec" \
-H "Content-Type: application/json" \
-d '{
  "calendar": "<EMAIL>",
  "message": "Test booking from troubleshooting",
  "userEmail": "<EMAIL>",
  "site": "odmustafa"
}'
```

## 🔍 Common Issues & Solutions

### Issue 1: HTML Response Instead of JSON
**Cause:** Web app not properly deployed or permissions not granted
**Solution:** Redeploy with "Anyone" access and grant all permissions

### Issue 2: "Script function not found"
**Cause:** Function name mismatch or script not saved
**Solution:** Ensure `doPost` function exists and script is saved

### Issue 3: Calendar Access Denied
**Cause:** Calendar permissions not granted
**Solution:** Grant Calendar API permissions during deployment

### Issue 4: Gmail Sending Failed
**Cause:** Gmail permissions not granted
**Solution:** Grant Gmail API permissions during deployment

## 🧪 Alternative: Simplified Test Version

If you're still having issues, try this simplified version first:

```javascript
function doPost(e) {
  try {
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        message: "Test endpoint working",
        receivedData: e.postData ? e.postData.contents : "No data"
      }))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doOptions(e) {
  return ContentService
    .createTextOutput('')
    .setHeaders({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    });
}
```

## 🔄 Redeployment Process

If you need to redeploy:

1. **Go to "Deploy" → "Manage deployments"**
2. **Click the pencil icon** next to your web app
3. **Change "Version" to "New version"**
4. **Click "Deploy"**
5. **Test the new URL**

## ✅ Expected Response

When working correctly, you should get:

```json
{
  "success": true,
  "bookingId": "event_id_here",
  "dateTime": "2025-01-02T14:00:00.000Z",
  "message": "Appointment scheduled successfully",
  "calendarOwner": "<EMAIL>"
}
```

## 🆘 If Still Not Working

1. **Check the Google Apps Script logs:**
   - Go to "Executions" tab in Apps Script
   - Look for error messages

2. **Try the test function:**
   - In Apps Script, run the `testBooking()` function
   - Check the logs for any errors

3. **Verify calendar access:**
   - Make sure the calendar emails exist and are accessible
   - Test with your own calendar first

## 📞 Quick Test Commands

Test the endpoint:
```bash
# Test basic connectivity
curl -X OPTIONS "YOUR_SCRIPT_URL"

# Test with minimal data
curl -X POST "YOUR_SCRIPT_URL" \
-H "Content-Type: application/json" \
-d '{"calendar": "<EMAIL>", "message": "test"}'
```

The key is ensuring the web app is deployed with "Anyone" access and all permissions are granted!
