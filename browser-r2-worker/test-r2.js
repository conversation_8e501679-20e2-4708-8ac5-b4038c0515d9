// Simple test to verify R2 bucket contents
// This would be run in a Worker environment, but we can verify via the dashboard

console.log("To verify the R2 bucket contents:");
console.log("1. Go to https://dash.cloudflare.com/");
console.log("2. Navigate to R2 Object Storage");
console.log("3. Click on 'html-bucket'");
console.log("4. You should see the HTML files we uploaded:");
console.log("   - developers.cloudflare.com_[timestamp].html");
console.log("   - www.elgatoai.com_[timestamp].html");

// Test URLs that were processed
const testResults = [
  {
    url: "https://developers.cloudflare.com/autorag/tutorial/brower-rendering-autorag-tutorial/",
    expectedKey: "developers.cloudflare.com_1751402778771.html",
    status: "✅ Successfully processed"
  },
  {
    url: "https://www.elgatoai.com",
    expectedKey: "www.elgatoai.com_1751402787447.html", 
    status: "✅ Successfully processed"
  }
];

console.log("\nTest Results:");
testResults.forEach(result => {
  console.log(`${result.status}: ${result.url}`);
  console.log(`Expected file: ${result.expectedKey}`);
  console.log("");
});

console.log("Worker URL: https://browser-r2-worker.elgatoaiagency.workers.dev");
console.log("R2 Bucket: html-bucket");
