{"name": "socks-proxy-agent", "version": "8.0.5", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "author": {"email": "<EMAIL>", "name": "<PERSON>", "url": "http://n8.io/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "repository": {"type": "git", "url": "https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/socks-proxy-agent"}, "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "socks": "^2.8.3"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "^4.1.7", "@types/dns2": "^2.0.3", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "cacheable-lookup": "^6.1.0", "dns2": "^2.1.0", "jest": "^29.5.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.2.0", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "license": "MIT", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}}