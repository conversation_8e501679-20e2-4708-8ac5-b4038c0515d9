{"version": 3, "file": "Input.d.ts", "sourceRoot": "", "sources": ["../../../../src/cdp/Input.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AACrD,OAAO,KAAK,EAAC,KAAK,EAAC,MAAM,yBAAyB,CAAC;AACnD,OAAO,EACL,QAAQ,EACR,KAAK,EAEL,WAAW,EACX,KAAK,cAAc,EACnB,KAAK,eAAe,EACpB,KAAK,mBAAmB,EACxB,KAAK,iBAAiB,EACtB,KAAK,gBAAgB,EACrB,KAAK,YAAY,EACjB,KAAK,iBAAiB,EACvB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAGL,KAAK,QAAQ,EACd,MAAM,+BAA+B,CAAC;AAOvC;;GAEG;AACH,qBAAa,WAAY,SAAQ,QAAQ;;IAIvC,UAAU,SAAK;gBAEH,MAAM,EAAE,UAAU;IAK9B,YAAY,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI;IAIvB,IAAI,CACjB,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE,QAAQ,CAAC,cAAc,CAG/B,GACA,OAAO,CAAC,IAAI,CAAC;IA6FD,EAAE,CAAC,GAAG,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAehC,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAIzD,OAAO,CAAC,SAAS;IAIF,IAAI,CACjB,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,QAAQ,CAAC,mBAAmB,CAAM,GAC1C,OAAO,CAAC,IAAI,CAAC;IAgBD,KAAK,CAClB,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE,QAAQ,CAAC,eAAe,CAAM,GACtC,OAAO,CAAC,IAAI,CAAC;CAUjB;AA6DD;;GAEG;AACH,qBAAa,QAAS,SAAQ,KAAK;;gBAIrB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW;IAMrD,YAAY,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI;IAqDvB,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAmBtB,IAAI,CACjB,CAAC,EAAE,MAAM,EACT,CAAC,EAAE,MAAM,EACT,OAAO,GAAE,QAAQ,CAAC,gBAAgB,CAAM,GACvC,OAAO,CAAC,IAAI,CAAC;IAwBD,IAAI,CAAC,OAAO,GAAE,QAAQ,CAAC,YAAY,CAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAyBzD,EAAE,CAAC,OAAO,GAAE,QAAQ,CAAC,YAAY,CAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAyBvD,KAAK,CAClB,CAAC,EAAE,MAAM,EACT,CAAC,EAAE,MAAM,EACT,OAAO,GAAE,QAAQ,CAAC,iBAAiB,CAAM,GACxC,OAAO,CAAC,IAAI,CAAC;IA0BD,KAAK,CAClB,OAAO,GAAE,QAAQ,CAAC,iBAAiB,CAAM,GACxC,OAAO,CAAC,IAAI,CAAC;IAcD,IAAI,CACjB,KAAK,EAAE,KAAK,EACZ,MAAM,EAAE,KAAK,GACZ,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;IAYpB,SAAS,CACtB,MAAM,EAAE,KAAK,EACb,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAC5B,OAAO,CAAC,IAAI,CAAC;IAUD,QAAQ,CACrB,MAAM,EAAE,KAAK,EACb,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAC5B,OAAO,CAAC,IAAI,CAAC;IAUD,IAAI,CACjB,MAAM,EAAE,KAAK,EACb,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAC5B,OAAO,CAAC,IAAI,CAAC;IAUD,WAAW,CACxB,KAAK,EAAE,KAAK,EACZ,MAAM,EAAE,KAAK,EACb,OAAO,GAAE;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAM,GAC7B,OAAO,CAAC,IAAI,CAAC;CAajB;AAED;;GAEG;AACH,qBAAa,cAAe,SAAQ,WAAW;;gBAIjC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW;IAMrD,YAAY,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI;IAIvB,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAgB/C,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAgB9C,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAOzC"}