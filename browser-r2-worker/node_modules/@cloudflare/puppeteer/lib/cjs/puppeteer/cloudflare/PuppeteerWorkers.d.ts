/**
 * @license
 * Copyright 2025 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import './globalPatcher.js';
import type { Browser } from '../api/Browser.js';
import type { ConnectOptions } from '../common/ConnectOptions.js';
import { Puppeteer } from '../common/Puppeteer.js';
import type { BrowserWorker } from './BrowserWorker.js';
import { type Locations } from './utils.js';
declare global {
    interface Response {
        readonly webSocket: WebSocket | null;
    }
    interface WebSocket {
        accept(): void;
    }
}
/**
 * @public
 */
export interface AcquireResponse {
    sessionId: string;
}
/**
 * @public
 */
export interface ActiveSession {
    sessionId: string;
    startTime: number;
    connectionId?: string;
    connectionStartTime?: string;
}
/**
 * @public
 */
export interface ClosedSession extends ActiveSession {
    endTime: number;
    closeReason: number;
    closeReasonText: string;
}
/**
 * @public
 */
export interface SessionsResponse {
    sessions: ActiveSession[];
}
/**
 * @public
 */
export interface HistoryResponse {
    history: ClosedSession[];
}
/**
 * @public
 */
export interface LimitsResponse {
    activeSessions: Array<{
        id: string;
    }>;
    maxConcurrentSessions: number;
    allowedBrowserAcquisitions: number;
    timeUntilNextAllowedBrowserAcquisition: number;
}
/**
 * @public
 */
export interface WorkersLaunchOptions {
    keep_alive?: number;
    location?: Locations;
}
/**
 * @public
 */
export declare class PuppeteerWorkers extends Puppeteer {
    constructor();
    /**
     * Launch a browser session.
     *
     * @param endpoint - Cloudflare worker binding
     * @returns a browser session or throws
     */
    launch(endpoint: BrowserWorker, options?: WorkersLaunchOptions): Promise<Browser>;
    /**
     * Returns active sessions
     *
     * @remarks
     * Sessions with a connnectionId already have a worker connection established
     *
     * @param endpoint - Cloudflare worker binding
     * @returns List of active sessions
     */
    sessions(endpoint: BrowserWorker): Promise<ActiveSession[]>;
    /**
     * Returns recent sessions (active and closed)
     *
     * @param endpoint - Cloudflare worker binding
     * @returns List of recent sessions (active and closed)
     */
    history(endpoint: BrowserWorker): Promise<ClosedSession[]>;
    /**
     * Returns current limits
     *
     * @param endpoint - Cloudflare worker binding
     * @returns current limits
     */
    limits(endpoint: BrowserWorker): Promise<LimitsResponse>;
    /**
     * Establish a devtools connection to an existing session
     *
     * @param endpoint - Cloudflare worker binding
     * @param sessionId - sessionId obtained from a .sessions() call
     * @returns a browser instance
     */
    connect(endpoint: BrowserWorker | ConnectOptions, sessionId?: string): Promise<Browser>;
}
//# sourceMappingURL=PuppeteerWorkers.d.ts.map