"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkersWebSocketTransport = void 0;
const chunking_js_1 = require("./chunking.js");
const FAKE_HOST = 'https://fake.host';
class WorkersWebSocketTransport {
    ws;
    pingInterval;
    chunks = [];
    onmessage;
    onclose;
    sessionId;
    static async create(endpoint, sessionId) {
        const path = `${FAKE_HOST}/v1/connectDevtools?browser_session=${sessionId}`;
        const response = await endpoint.fetch(path, {
            headers: { Upgrade: 'websocket' },
        });
        response.webSocket.accept();
        return new WorkersWebSocketTransport(response.webSocket, sessionId);
    }
    constructor(ws, sessionId) {
        this.pingInterval = setInterval(() => {
            return this.ws.send('ping');
        }, 1000); // TODO more investigation
        this.ws = ws;
        this.sessionId = sessionId;
        this.ws.addEventListener('message', event => {
            this.chunks.push(new Uint8Array(event.data));
            const message = (0, chunking_js_1.chunksToMessage)(this.chunks, sessionId);
            if (message && this.onmessage) {
                this.onmessage(message);
            }
        });
        this.ws.addEventListener('close', () => {
            clearInterval(this.pingInterval);
            if (this.onclose) {
                this.onclose();
            }
        });
        this.ws.addEventListener('error', e => {
            console.error(`Websocket error: SessionID: ${sessionId}`, e);
            clearInterval(this.pingInterval);
        });
    }
    send(message) {
        for (const chunk of (0, chunking_js_1.messageToChunks)(message)) {
            this.ws.send(chunk);
        }
    }
    close() {
        clearInterval(this.pingInterval);
        this.ws.close();
    }
    toString() {
        return this.sessionId;
    }
}
exports.WorkersWebSocketTransport = WorkersWebSocketTransport;
//# sourceMappingURL=WorkersWebSocketTransport.js.map