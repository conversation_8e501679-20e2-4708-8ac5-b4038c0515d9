"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.connectToCDPBrowser = exports.DEFAULT_VIEWPORT = void 0;
/**
 * @license
 * Copyright 2025 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
exports.DEFAULT_VIEWPORT = Object.freeze({ width: 800, height: 600 });
const Browser_js_1 = require("../cdp/Browser.js");
const Connection_js_1 = require("../cdp/Connection.js");
/**
 * Users should never call this directly; it's called when calling
 * `puppeteer.connect` with `protocol: 'cdp'`.
 *
 *
 */
async function connectToCDPBrowser(connectionTransport, options) {
    const { ignoreHTTPSErrors = false, defaultViewport = exports.DEFAULT_VIEWPORT, targetFilter, _isPageTarget: isPageTarget, slowMo = 0, protocolTimeout, sessionId = 'unknown', } = options;
    const connection = new Connection_js_1.Connection('', connectionTransport, slowMo, protocolTimeout);
    const version = await connection.send('Browser.getVersion');
    const product = version.product.toLowerCase().includes('firefox')
        ? 'firefox'
        : 'chrome';
    const { browserContextIds } = await connection.send('Target.getBrowserContexts');
    const browser = await Browser_js_1.CdpBrowser._create(product || 'chrome', connection, browserContextIds, ignoreHTTPSErrors, defaultViewport, undefined, () => {
        return connection.send('Browser.close').catch(console.log);
    }, targetFilter, isPageTarget, true, sessionId);
    return browser;
}
exports.connectToCDPBrowser = connectToCDPBrowser;
//# sourceMappingURL=utils.js.map