/// <reference types="node" />
/**
 * @license
 * Copyright 2025 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ConnectionTransport } from '../common/ConnectionTransport.js';
import type { <PERSON><PERSON>erWorker } from './BrowserWorker.js';
export declare class WorkersWebSocketTransport implements ConnectionTransport {
    ws: WebSocket;
    pingInterval: NodeJS.Timer;
    chunks: Uint8Array[];
    onmessage?: (message: string) => void;
    onclose?: () => void;
    sessionId: string;
    static create(endpoint: BrowserWorker, sessionId: string): Promise<WorkersWebSocketTransport>;
    constructor(ws: WebSocket, sessionId: string);
    send(message: string): void;
    close(): void;
    toString(): string;
}
//# sourceMappingURL=WorkersWebSocketTransport.d.ts.map