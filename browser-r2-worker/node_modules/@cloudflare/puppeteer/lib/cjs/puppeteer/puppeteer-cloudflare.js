"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sessions = exports.limits = exports.launch = exports.history = exports.connect = void 0;
/**
 * @license
 * Copyright 2025 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const PuppeteerWorkers_js_1 = require("./cloudflare/PuppeteerWorkers.js");
__exportStar(require("./api/api.js"), exports);
__exportStar(require("./common/common.js"), exports);
__exportStar(require("./revisions.js"), exports);
__exportStar(require("./util/util.js"), exports);
__exportStar(require("./cloudflare/BrowserWorker.js"), exports);
const puppeteer = new PuppeteerWorkers_js_1.PuppeteerWorkers();
exports.connect = puppeteer.connect, exports.history = puppeteer.history, exports.launch = puppeteer.launch, exports.limits = puppeteer.limits, exports.sessions = puppeteer.sessions;
__exportStar(require("./cloudflare/PuppeteerWorkers.js"), exports);
exports.default = puppeteer;
//# sourceMappingURL=puppeteer-cloudflare.js.map