{"name": "elgatoai-bot", "version": "1.0.0", "main": "src/index.js", "type": "module", "scripts": {"test": "node test-setup.js", "deploy": "wrangler deploy", "dev": "wrangler dev", "setup-workers-mcp": "node setup-workers-mcp.js", "setup-cloudflare-mcp": "npx @cloudflare/mcp-server-cloudflare init 36f951ec821870bc8a454bad524a4b17", "setup-booking-bot": "node setup-booking-bot.js", "test-booking-bot": "node test-booking-bot.js", "create-kv": "wrangler kv:namespace create CHAT_SESSIONS", "preview": "wrangler dev --local"}, "keywords": ["mcp", "cloudflare", "workers", "ai", "claude"], "author": "<PERSON>", "license": "ISC", "description": "ElgatoAI Bot with Cloudflare MCP integration", "dependencies": {"@cloudflare/mcp-server-cloudflare": "^0.2.0", "@modelcontextprotocol/sdk": "^1.13.2", "workers-mcp": "^0.0.13"}}