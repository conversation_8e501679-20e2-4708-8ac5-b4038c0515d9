# 🔧 Fix Calendar Integration - Step by Step

## 🚨 Current Issue
Your Google Apps Script is returning HTML instead of JSON, indicating a deployment problem.

## ✅ Quick Fix Steps

### Step 1: Create New Google Apps Script
1. **Go to [script.google.com](https://script.google.com/)**
2. **Click "New Project"**
3. **Delete the default code**
4. **Copy and paste the code from `google-apps-script-simple.gs`**
5. **Save the project** (Ctrl+S or Cmd+S)
6. **Name it "ElgatoAI Calendar Integration"**

### Step 2: Deploy Correctly
1. **Click "Deploy" → "New deployment"**
2. **Click the gear icon** next to "Select type"
3. **Choose "Web app"**
4. **Configure deployment:**
   - **Description:** "ElgatoAI Calendar API"
   - **Execute as:** "Me (<EMAIL>)"
   - **Who has access:** "Anyone" ⚠️ **This is critical!**
5. **Click "Deploy"**

### Step 3: Grant Permissions
1. **You'll see "Authorization required"**
2. **Click "Review permissions"**
3. **Choose your Google account**
4. **You may see "Google hasn't verified this app"**
5. **Click "Advanced"**
6. **Click "Go to ElgatoAI Calendar Integration (unsafe)"**
7. **Click "Allow"** for all requested permissions

### Step 4: Get the New URL
1. **Copy the Web app URL** (it will be different from your current one)
2. **It should look like:** `https://script.google.com/macros/s/NEW_ID_HERE/exec`

### Step 5: Update Your Worker
1. **Update your `wrangler.toml` or environment variables**
2. **Replace the old URL with the new one**
3. **Redeploy your worker:** `npm run deploy`

### Step 6: Test the New Endpoint

Run this test command with your NEW URL:

```bash
curl -X POST "YOUR_NEW_SCRIPT_URL" \
-H "Content-Type: application/json" \
-d '{
  "calendar": "<EMAIL>",
  "message": "Test booking",
  "userEmail": "<EMAIL>",
  "site": "odmustafa"
}'
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Calendar endpoint is working! (Test mode - no actual booking created)",
  "receivedData": {
    "calendar": "<EMAIL>",
    "message": "Test booking",
    "userEmail": "<EMAIL>",
    "site": "odmustafa"
  },
  "timestamp": "2025-01-02T17:00:00.000Z",
  "testMode": true
}
```

## 🔍 Troubleshooting

### If you still get HTML:
1. **Check the URL** - make sure you're using the web app URL, not the script editor URL
2. **Verify deployment** - go to "Deploy" → "Manage deployments" and check status
3. **Check permissions** - make sure "Who has access" is set to "Anyone"

### If you get permission errors:
1. **Redeploy** with a new version
2. **Grant permissions** again
3. **Make sure you're signed in** to the correct Google account

### If the script has errors:
1. **Check the "Executions" tab** in Apps Script for error logs
2. **Run the `testEndpoint()` function** manually in the script editor
3. **Check the logs** for any error messages

## 🎯 Once Working

After the simple version works, you can:

1. **Replace the `doPost` function** with the full version from the comments
2. **Grant Calendar and Gmail permissions**
3. **Test actual calendar booking**

## 📝 Common Mistakes

❌ **Wrong access setting** - Must be "Anyone", not "Anyone with Google account"
❌ **Using script editor URL** - Must use the web app deployment URL
❌ **Not granting permissions** - Must click through all permission dialogs
❌ **Old deployment** - Must create new deployment if changing code

## ✅ Success Indicators

- ✅ POST request returns JSON (not HTML)
- ✅ Response includes `"success": true`
- ✅ No CORS errors in browser
- ✅ Worker can communicate with the endpoint

## 🔄 If You Need to Start Over

1. **Delete the current deployment**
2. **Create a completely new Google Apps Script project**
3. **Follow all steps exactly as written**
4. **Test each step before proceeding**

The key is ensuring the web app is properly deployed with "Anyone" access!
