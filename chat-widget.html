<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ElgatoAI Chat Widget</title>
    <style>
        /* Chat Widget Styles */
        #elgatoai-chatbox {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        #elgatoai-chatbox.minimized {
            height: 60px;
            width: 300px;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .chat-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .chat-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        #elgatoai-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 12px;
            padding: 10px 12px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            line-height: 1.4;
            font-size: 14px;
        }

        .message.user {
            background: #667eea;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.bot {
            background: white;
            color: #333;
            border: 1px solid #e1e5e9;
        }

        .chat-input-container {
            padding: 15px;
            background: white;
            border-top: 1px solid #e1e5e9;
            border-radius: 0 0 10px 10px;
        }

        .chat-input-row {
            display: flex;
            gap: 8px;
        }

        #elgatoai-msg {
            flex: 1;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
            font-size: 14px;
            font-family: inherit;
        }

        #elgatoai-msg:focus {
            border-color: #667eea;
        }

        .send-button {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .send-button:hover {
            background: #5a6fd8;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            padding: 10px 12px;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 18px;
            max-width: 80%;
            margin-bottom: 12px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) { animation-delay: 0.2s; }
        .typing-dots span:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .email-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 15px;
            margin-top: 8px;
            font-size: 13px;
            outline: none;
        }

        .email-input:focus {
            border-color: #667eea;
        }

        /* Mobile responsiveness */
        @media (max-width: 480px) {
            #elgatoai-chatbox {
                width: calc(100vw - 40px);
                height: calc(100vh - 40px);
                bottom: 20px;
                right: 20px;
                left: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Chat Widget -->
    <div id="elgatoai-chatbox">
        <div class="chat-header" onclick="toggleChat()">
            <h3>💬 ElgatoAI Assistant</h3>
            <button class="chat-toggle" id="chat-toggle">−</button>
        </div>
        <div class="chat-body" id="chat-body">
            <div id="elgatoai-messages">
                <div class="message bot">
                    👋 Hi! I'm your ElgatoAI assistant. I can help you book consultations, answer questions about our services, or send you detailed information. How can I help you today?
                </div>
            </div>
            <div class="typing-indicator" id="typing-indicator">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
            <div class="chat-input-container">
                <div class="chat-input-row">
                    <input 
                        id="elgatoai-msg" 
                        placeholder="Type your message..." 
                        onkeypress="handleKeyPress(event)"
                    />
                    <button class="send-button" onclick="sendMsg()" id="send-button">
                        ➤
                    </button>
                </div>
                <input 
                    id="user-email" 
                    class="email-input" 
                    placeholder="Your email (optional, for booking confirmations)"
                    style="display: none;"
                />
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const WORKER_URL = 'https://elgatoai-bot.your-subdomain.workers.dev';
        let isMinimized = false;
        let userEmail = '';
        let awaitingEmail = false;

        // Initialize chat
        function initChat() {
            // Auto-detect site context
            const hostname = window.location.hostname;
            window.chatSite = hostname.includes('mustafa') ? 'odmustafa' : 'elgatoai';
            
            // Load chat history from localStorage
            loadChatHistory();
        }

        // Toggle chat window
        function toggleChat() {
            const chatbox = document.getElementById('elgatoai-chatbox');
            const chatBody = document.getElementById('chat-body');
            const toggle = document.getElementById('chat-toggle');
            
            isMinimized = !isMinimized;
            
            if (isMinimized) {
                chatbox.classList.add('minimized');
                chatBody.style.display = 'none';
                toggle.textContent = '+';
            } else {
                chatbox.classList.remove('minimized');
                chatBody.style.display = 'flex';
                toggle.textContent = '−';
            }
        }

        // Handle Enter key press
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMsg();
            }
        }

        // Send message
        async function sendMsg() {
            const msgInput = document.getElementById('elgatoai-msg');
            const emailInput = document.getElementById('user-email');
            const sendButton = document.getElementById('send-button');
            const msg = msgInput.value.trim();
            
            if (!msg) return;

            // Get user email if provided
            if (emailInput.style.display !== 'none') {
                userEmail = emailInput.value.trim();
            }

            // Add user message to chat
            addMessage(msg, 'user');
            msgInput.value = '';
            
            // Disable send button and show typing indicator
            sendButton.disabled = true;
            showTypingIndicator();

            try {
                // Send to worker
                const response = await fetch(WORKER_URL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: msg,
                        site: window.chatSite,
                        userEmail: userEmail
                    })
                });

                const data = await response.json();
                
                // Hide typing indicator
                hideTypingIndicator();
                
                // Add bot response
                addMessage(data.response || data.error || 'Sorry, I encountered an error.', 'bot');
                
                // Show email input for booking/brochure requests if not already provided
                if (!userEmail && (msg.toLowerCase().includes('book') || msg.toLowerCase().includes('brochure'))) {
                    showEmailInput();
                }
                
                // Save chat history
                saveChatHistory();
                
            } catch (error) {
                console.error('Error:', error);
                hideTypingIndicator();
                addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'bot');
            } finally {
                sendButton.disabled = false;
            }
        }

        // Add message to chat
        function addMessage(text, sender) {
            const messagesDiv = document.getElementById('elgatoai-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.textContent = text;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Show typing indicator
        function showTypingIndicator() {
            const indicator = document.getElementById('typing-indicator');
            indicator.style.display = 'block';
            const messagesDiv = document.getElementById('elgatoai-messages');
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Hide typing indicator
        function hideTypingIndicator() {
            const indicator = document.getElementById('typing-indicator');
            indicator.style.display = 'none';
        }

        // Show email input
        function showEmailInput() {
            const emailInput = document.getElementById('user-email');
            emailInput.style.display = 'block';
            emailInput.focus();
        }

        // Save chat history to localStorage
        function saveChatHistory() {
            const messages = Array.from(document.querySelectorAll('.message')).map(msg => ({
                text: msg.textContent,
                sender: msg.classList.contains('user') ? 'user' : 'bot'
            }));
            localStorage.setItem('elgatoai-chat-history', JSON.stringify(messages));
        }

        // Load chat history from localStorage
        function loadChatHistory() {
            const history = localStorage.getItem('elgatoai-chat-history');
            if (history) {
                const messages = JSON.parse(history);
                const messagesDiv = document.getElementById('elgatoai-messages');
                messagesDiv.innerHTML = ''; // Clear default message
                
                messages.forEach(msg => {
                    addMessage(msg.text, msg.sender);
                });
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initChat);
    </script>
</body>
</html>
