name = "elgatoai-bot"
main = "src/index.js"
compatibility_date = "2024-06-01"

# Account configuration
account_id = "36f951ec821870bc8a454bad524a4b17"

# Worker configuration
workers_dev = true

# AI binding for Workers AI
[ai]
binding = "AI"

# Environment variables
[vars]
GATEWAY_ID = "elgato-gateway"
API_TOKEN = "6KsLbbnW4TV5erL0jv2jF_FmA0WHk0WVFVSGbci1"
CALENDAR_API_ENDPOINT = "https://script.google.com/macros/s/AKfycbyxEPgRE2sZY9wflgvL7My2Yjf6JAOCuSki6Gvu3EPxEX5qv9aco5sAJi5gtQTTaZ4/exec"

# KV namespace for storing chat sessions (optional)
[[kv_namespaces]]
binding = "KV_BINDING"
id = "35b39e46374d42bf979af86a10dd2aec"
preview_id = "35b39e46374d42bf979af86a10dd2aec"

# Compatibility flags
compatibility_flags = ["nodejs_compat"]