name = "elgatoai-bot"
main = "src/index.js"
compatibility_date = "2024-06-01"

# Account configuration
account_id = "36f951ec821870bc8a454bad524a4b17"

# Worker configuration
workers_dev = true

# AI binding for Workers AI
[ai]
binding = "AI"

# Environment variables
[vars]
GATEWAY_ID = "your_gateway_id_here"
API_TOKEN = "your_api_token_here"
CALENDAR_API_ENDPOINT = "https://your-calendar-api-endpoint.com"

# KV namespace for storing chat sessions (optional)
[[kv_namespaces]]
binding = "CHAT_SESSIONS"
id = "your-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"
