name = "elgatoai-bot"
main = "src/index.js"
compatibility_date = "2024-06-01"

# Account configuration
account_id = "69e81d1f8adf631c8db1c6eaf05ee07b"

# Worker configuration
workers_dev = true

# AI binding for Workers AI
[ai]
binding = "AI"

# Environment variables
[vars]
GATEWAY_ID = "elgato-gateway"
API_TOKEN = "6KsLbbnW4TV5erL0jv2jF_FmA0WHk0WVFVSGbci1"
CALENDAR_API_ENDPOINT = "https://script.google.com/macros/s/AKfycbzmTmeMHbXbDwoB5Odz5E93L-ct5jRlXVdsJbxZ7Xlg6XIm1v8vC9Muy2pycQErj_np/exec"

# KV namespace for storing chat sessions (optional)
[[kv_namespaces]]
binding = "KV_BINDING"
id = "35b39e46374d42bf979af86a10dd2aec"
preview_id = "35b39e46374d42bf979af86a10dd2aec"

# Browser R2 Worker Example
compatibility_flags = "nodejs_compat",
  "browser": {
    "binding": "MY_BROWSER",
  },
  "r2_buckets": [
    {
      "binding": "HTML_BUCKET",
      "bucket_name": "html-bucket",
    },
  ],
}