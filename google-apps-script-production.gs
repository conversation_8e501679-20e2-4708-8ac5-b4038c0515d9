/**
 * PRODUCTION Google Apps Script for Calendar Integration
 * This version creates actual calendar events and sends emails
 * 
 * IMPORTANT: This will create real calendar events!
 * Make sure you're ready for production use.
 */

function doPost(e) {
  try {
    console.log('=== PRODUCTION POST Request Received ===');
    console.log('Event object:', JSON.stringify(e, null, 2));
    
    // Handle different ways POST data might come in
    let data = {};
    
    if (e.postData) {
      console.log('Post data found:', e.postData.contents);
      
      if (e.postData.type === 'application/json') {
        data = JSON.parse(e.postData.contents);
      } else if (e.postData.type === 'application/x-www-form-urlencoded') {
        // Handle form data
        const params = new URLSearchParams(e.postData.contents);
        if (params.has('payload')) {
          data = JSON.parse(params.get('payload'));
        } else {
          // Convert form data to object
          for (const [key, value] of params) {
            data[key] = value;
          }
        }
      } else {
        // Try to parse as JSON anyway
        try {
          data = JSON.parse(e.postData.contents);
        } catch (parseError) {
          console.log('Failed to parse as JSON:', parseError);
          data = { rawData: e.postData.contents };
        }
      }
    }
    
    // Also check parameters (for GET-style parameters in POST)
    if (e.parameter) {
      console.log('Parameters found:', JSON.stringify(e.parameter));
      Object.assign(data, e.parameter);
    }
    
    console.log('Parsed data:', JSON.stringify(data));
    
    const { calendar, message, userEmail, site } = data;
    
    // Validate required fields
    if (!calendar || !message) {
      throw new Error('Missing required fields: calendar and message');
    }
    
    // Determine which calendar to use
    const calendarId = calendar === '<EMAIL>' ? 
      '<EMAIL>' : '<EMAIL>';
    
    console.log(`Using calendar: ${calendarId}`);
    
    // Get the calendar
    const cal = CalendarApp.getCalendarById(calendarId);
    
    if (!cal) {
      throw new Error(`Calendar not found: ${calendarId}. Make sure the calendar exists and is accessible.`);
    }
    
    // Find next available business day slot
    const appointmentTime = findNextAvailableSlot(cal);
    console.log(`Appointment time: ${appointmentTime}`);
    
    // Create the calendar event
    const event = createCalendarEvent(cal, appointmentTime, site, message, userEmail);
    console.log(`Event created: ${event.getId()}`);
    
    // Send confirmation email if user email provided
    if (userEmail && isValidEmail(userEmail)) {
      sendConfirmationEmail(userEmail, appointmentTime, site, calendar, event.getId());
      console.log(`Confirmation email sent to: ${userEmail}`);
    }
    
    // Send notification to calendar owner
    sendOwnerNotification(calendar, appointmentTime, message, userEmail, site);
    console.log(`Owner notification sent to: ${calendar}`);
    
    // Return success response
    const response = {
      success: true,
      bookingId: event.getId(),
      dateTime: appointmentTime.toISOString(),
      message: 'Appointment scheduled successfully',
      calendarOwner: calendar,
      appointmentDetails: {
        date: appointmentTime.toDateString(),
        time: appointmentTime.toLocaleTimeString(),
        duration: '1 hour',
        location: 'Video Call (link will be provided)'
      }
    };
    
    console.log('Sending success response:', JSON.stringify(response));
    
    const output = ContentService.createTextOutput(JSON.stringify(response));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
      
  } catch (error) {
    console.error('Error in doPost:', error);
    
    const errorResponse = {
      success: false,
      error: error.toString(),
      message: 'Failed to schedule appointment',
      timestamp: new Date().toISOString(),
      debug: {
        hasPostData: !!e.postData,
        hasParameters: !!e.parameter,
        postDataType: e.postData ? e.postData.type : 'None'
      }
    };
    
    const output = ContentService.createTextOutput(JSON.stringify(errorResponse));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
  }
}

/**
 * Find the next available time slot
 */
function findNextAvailableSlot(calendar) {
  const now = new Date();
  let appointmentTime = new Date();
  
  // Start from tomorrow at 2 PM
  appointmentTime.setDate(now.getDate() + 1);
  appointmentTime.setHours(14, 0, 0, 0);
  
  // Skip weekends
  while (appointmentTime.getDay() === 0 || appointmentTime.getDay() === 6) {
    appointmentTime.setDate(appointmentTime.getDate() + 1);
  }
  
  // Check for conflicts and find next available slot
  let attempts = 0;
  const maxAttempts = 14; // Check up to 2 weeks ahead
  
  while (attempts < maxAttempts) {
    const endTime = new Date(appointmentTime.getTime() + (60 * 60 * 1000)); // 1 hour meeting
    const events = calendar.getEvents(appointmentTime, endTime);
    
    if (events.length === 0) {
      // No conflicts, this slot is available
      console.log(`Found available slot: ${appointmentTime}`);
      break;
    }
    
    console.log(`Conflict found at ${appointmentTime}, trying next hour`);
    
    // Move to next hour
    appointmentTime.setHours(appointmentTime.getHours() + 1);
    
    // If past business hours (6 PM), move to next day at 9 AM
    if (appointmentTime.getHours() >= 18) {
      appointmentTime.setDate(appointmentTime.getDate() + 1);
      appointmentTime.setHours(9, 0, 0, 0);
      
      // Skip weekends
      while (appointmentTime.getDay() === 0 || appointmentTime.getDay() === 6) {
        appointmentTime.setDate(appointmentTime.getDate() + 1);
      }
    }
    
    attempts++;
  }
  
  if (attempts >= maxAttempts) {
    throw new Error('No available slots found in the next 2 weeks');
  }
  
  return appointmentTime;
}

/**
 * Create a calendar event
 */
function createCalendarEvent(calendar, startTime, site, message, userEmail) {
  const endTime = new Date(startTime.getTime() + (60 * 60 * 1000)); // 1 hour meeting
  const title = `Consultation - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
  
  const description = `
Booking Request: ${message}

User Email: ${userEmail || 'Not provided'}
Site: ${site}
Booked via: ElgatoAI Chat Bot

Meeting Details:
- Duration: 1 hour
- Type: Consultation
- Status: Confirmed
- Booking ID: Will be generated after creation
  `.trim();
  
  const event = calendar.createEvent(title, startTime, endTime, {
    description: description,
    guests: userEmail ? [userEmail] : [],
    sendInvites: !!userEmail,
    location: 'Video Call (link will be provided)'
  });
  
  console.log(`Calendar event created: ${event.getTitle()} at ${startTime}`);
  return event;
}

/**
 * Send confirmation email to user
 */
function sendConfirmationEmail(userEmail, appointmentTime, site, calendarOwner, eventId) {
  const subject = `Appointment Confirmed - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
  
  const body = `
Dear Client,

Thank you for booking a consultation with ${site === 'odmustafa' ? 'Omar Mustafa' : 'the ElgatoAI team'}!

Your appointment has been scheduled for:
📅 Date: ${appointmentTime.toDateString()}
🕐 Time: ${appointmentTime.toLocaleTimeString()}
⏱️ Duration: 1 hour
📧 Calendar: ${calendarOwner}

What to expect:
- You should receive a calendar invitation shortly
- We'll send you a video call link 15 minutes before the meeting
- Please prepare any questions or materials you'd like to discuss

Need to reschedule or cancel?
Simply reply to this email or contact us directly.

We look forward to speaking with you!

Best regards,
${site === 'odmustafa' ? 'Omar Mustafa' : 'The ElgatoAI Team'}

---
Booking ID: ${eventId}
Booked via: ElgatoAI Chat Assistant
  `.trim();
  
  try {
    GmailApp.sendEmail(userEmail, subject, body);
    console.log(`Confirmation email sent to: ${userEmail}`);
  } catch (error) {
    console.error('Failed to send confirmation email:', error);
  }
}

/**
 * Send notification to calendar owner
 */
function sendOwnerNotification(calendarOwner, appointmentTime, message, userEmail, site) {
  const subject = `New Booking: ${appointmentTime.toDateString()} at ${appointmentTime.toLocaleTimeString()}`;
  
  const body = `
New consultation booking received!

📅 Date: ${appointmentTime.toDateString()}
🕐 Time: ${appointmentTime.toLocaleTimeString()}
👤 Client Email: ${userEmail || 'Not provided'}
🌐 Site: ${site}

Original Message:
"${message}"

The appointment has been automatically added to your calendar.

${userEmail ? 'A confirmation email has been sent to the client.' : 'No client email provided - you may want to follow up.'}

---
Booked via: ElgatoAI Chat Bot
  `.trim();
  
  try {
    GmailApp.sendEmail(calendarOwner, subject, body);
    console.log(`Owner notification sent to: ${calendarOwner}`);
  } catch (error) {
    console.error('Failed to send owner notification:', error);
  }
}

/**
 * Handle GET requests (for testing)
 */
function doGet(e) {
  console.log('=== GET Request Received ===');
  console.log('Parameters:', JSON.stringify(e.parameter));
  
  const response = {
    message: 'ElgatoAI Calendar API is running in PRODUCTION mode',
    status: 'OK',
    timestamp: new Date().toISOString(),
    note: 'Send POST requests to this endpoint with calendar booking data',
    warning: 'This endpoint creates REAL calendar events!',
    testEndpoint: 'Use POST with JSON: {"calendar": "<EMAIL>", "message": "test", "userEmail": "<EMAIL>", "site": "elgatoai"}',
    parameters: e.parameter || {}
  };
  
  const output = ContentService.createTextOutput(JSON.stringify(response));
  output.setMimeType(ContentService.MimeType.JSON);
  
  return output;
}

/**
 * Validate email address
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Test function - BE CAREFUL! This creates real calendar events
 */
function testProductionBooking() {
  console.log('⚠️ WARNING: This will create a REAL calendar event!');
  
  const testData = {
    calendar: '<EMAIL>',
    message: 'TEST BOOKING - Please delete this event',
    userEmail: '<EMAIL>',
    site: 'odmustafa'
  };
  
  const mockEvent = {
    postData: {
      contents: JSON.stringify(testData),
      type: 'application/json'
    }
  };
  
  const result = doPost(mockEvent);
  console.log('Test result:', result.getContent());
  
  return result.getContent();
}
