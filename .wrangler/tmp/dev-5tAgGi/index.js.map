{"version": 3, "sources": ["../bundle-oNEGZA/checked-fetch.js", "../../../src/index.js", "../../../../../../.nvm/versions/node/v23.11.0/lib/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../../../../.nvm/versions/node/v23.11.0/lib/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-oNEGZA/middleware-insertion-facade.js", "../../../../../../.nvm/versions/node/v23.11.0/lib/node_modules/wrangler/templates/middleware/common.ts", "../bundle-oNEGZA/middleware-loader.entry.ts"], "sourceRoot": "/Users/<USER>/Documents/augment-projects/elgatoai-bot/.wrangler/tmp/dev-5tAgGi", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "/**\n * ElgatoAI Booking Bot - Cloudflare Worker\n * Handles chat interactions, booking requests, and multi-site routing\n */\n\nexport default {\n  async fetch(request, env, ctx) {\n    // Handle CORS preflight requests\n    if (request.method === 'OPTIONS') {\n      return new Response(null, {\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type',\n        },\n      });\n    }\n\n    // Only handle POST requests for chat\n    if (request.method !== 'POST') {\n      return new Response('Method not allowed', { status: 405 });\n    }\n\n    try {\n      const { site, message, userEmail } = await request.json();\n\n      // Validate input\n      if (!message) {\n        return new Response(JSON.stringify({\n          error: 'Message is required'\n        }), {\n          status: 400,\n          headers: {\n            'Content-Type': 'application/json',\n            'Access-Control-Allow-Origin': '*'\n          }\n        });\n      }\n\n      // Determine site context\n      const siteContext = site || (message.toLowerCase().includes('mustafa') ? 'odmustafa' : 'elgatoai');\n\n      // Create AI prompt based on site and message\n      const aiPrompt = createAIPrompt(siteContext, message);\n\n      // Get AI response using Workers AI\n      const aiResponse = await env.AI.run('@cf/meta/llama-3.1-8b-instruct', {\n        prompt: aiPrompt,\n        max_tokens: 256\n      });\n\n      // Handle booking requests\n      if (isBookingRequest(message)) {\n        return await handleBookingRequest(message, siteContext, userEmail, env);\n      }\n\n      // Handle brochure requests\n      if (isBrochureRequest(message)) {\n        return await handleBrochureRequest(message, siteContext, userEmail, env);\n      }\n\n      // Handle general inquiries\n      if (isGeneralInquiry(message)) {\n        return await handleGeneralInquiry(message, siteContext, aiResponse, env);\n      }\n\n      // Default AI response\n      return new Response(JSON.stringify({\n        response: aiResponse.response || \"I'm here to help you with booking appointments and answering questions about our services. How can I assist you today?\"\n      }), {\n        headers: {\n          'Content-Type': 'application/json',\n          'Access-Control-Allow-Origin': '*'\n        }\n      });\n\n    } catch (error) {\n      console.error('Error processing request:', error);\n      return new Response(JSON.stringify({\n        error: 'Internal server error',\n        response: \"I'm sorry, I'm experiencing some technical difficulties. Please try again in a moment.\"\n      }), {\n        status: 500,\n        headers: {\n          'Content-Type': 'application/json',\n          'Access-Control-Allow-Origin': '*'\n        }\n      });\n    }\n  }\n};\n\n/**\n * Create AI prompt based on site context and user message\n */\nfunction createAIPrompt(site, message) {\n  const siteInfo = {\n    elgatoai: {\n      name: \"ElgatoAI\",\n      services: \"AI consulting, automation solutions, and digital transformation services\",\n      contact: \"<EMAIL>\",\n      specialty: \"helping businesses leverage AI technology\"\n    },\n    odmustafa: {\n      name: \"Omar Mustafa\",\n      services: \"personal AI consulting and technical advisory services\",\n      contact: \"<EMAIL>\",\n      specialty: \"AI strategy and implementation consulting\"\n    }\n  };\n\n  const info = siteInfo[site] || siteInfo.elgatoai;\n\n  return `You are a professional booking assistant for ${info.name}. \nWe specialize in ${info.services}, focusing on ${info.specialty}.\n\nUser message: \"${message}\"\n\nRespond professionally and helpfully. If they want to:\n- Book an appointment: Guide them through the booking process\n- Request a brochure: Confirm we'll send them information\n- Ask about services: Explain our ${info.services}\n- General questions: Provide helpful, relevant information\n\nKeep responses concise (under 100 words) and friendly. Always offer to help with booking or provide more information.`;\n}\n\n/**\n * Check if message is a booking request\n */\nfunction isBookingRequest(message) {\n  const bookingKeywords = ['book', 'appointment', 'schedule', 'meeting', 'consultation', 'call', 'session'];\n  return bookingKeywords.some(keyword => message.toLowerCase().includes(keyword));\n}\n\n/**\n * Check if message is a brochure request\n */\nfunction isBrochureRequest(message) {\n  const brochureKeywords = ['brochure', 'information', 'details', 'pdf', 'document', 'materials'];\n  return brochureKeywords.some(keyword => message.toLowerCase().includes(keyword));\n}\n\n/**\n * Check if message is a general inquiry\n */\nfunction isGeneralInquiry(message) {\n  const inquiryKeywords = ['what', 'how', 'why', 'when', 'where', 'services', 'help', 'about'];\n  return inquiryKeywords.some(keyword => message.toLowerCase().includes(keyword));\n}\n\n/**\n * Handle booking requests\n */\nasync function handleBookingRequest(message, site, userEmail, env) {\n  const calendarOwner = site === 'odmustafa' ? '<EMAIL>' : '<EMAIL>';\n\n  try {\n    // Call calendar API to book appointment\n    const calendarResponse = await fetch(env.CALENDAR_API_ENDPOINT, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        calendar: calendarOwner,\n        message,\n        userEmail,\n        site\n      })\n    });\n\n    if (calendarResponse.ok) {\n      const bookingData = await calendarResponse.json();\n      return new Response(JSON.stringify({\n        response: `Great! I've initiated the booking process for ${calendarOwner}. You should receive a confirmation email shortly with available time slots. If you provided your email, we'll send you a calendar invite once confirmed.`,\n        bookingId: bookingData.bookingId || null\n      }), {\n        headers: {\n          'Content-Type': 'application/json',\n          'Access-Control-Allow-Origin': '*'\n        }\n      });\n    } else {\n      throw new Error('Calendar API error');\n    }\n  } catch (error) {\n    console.error('Booking error:', error);\n    return new Response(JSON.stringify({\n      response: `I'd be happy to help you book an appointment with ${calendarOwner}. Please provide your email address and preferred time, and I'll get back to you with available slots within 24 hours.`\n    }), {\n      headers: {\n        'Content-Type': 'application/json',\n        'Access-Control-Allow-Origin': '*'\n      }\n    });\n  }\n}\n\n/**\n * Handle brochure requests\n */\nasync function handleBrochureRequest(message, site, userEmail, env) {\n  // In a real implementation, you would integrate with an email service\n  // For now, we'll provide a helpful response\n\n  const siteInfo = site === 'odmustafa' ? 'Omar Mustafa\\'s consulting services' : 'ElgatoAI\\'s services';\n\n  return new Response(JSON.stringify({\n    response: `I'd be happy to send you detailed information about ${siteInfo}! Please provide your email address and I'll send you our comprehensive brochure with service details, case studies, and pricing information.`\n  }), {\n    headers: {\n      'Content-Type': 'application/json',\n      'Access-Control-Allow-Origin': '*'\n    }\n  });\n}\n\n/**\n * Handle general inquiries\n */\nasync function handleGeneralInquiry(message, site, aiResponse, env) {\n  return new Response(JSON.stringify({\n    response: aiResponse.response + \"\\n\\nWould you like to book a consultation or receive more detailed information about our services?\"\n  }), {\n    headers: {\n      'Content-Type': 'application/json',\n      'Access-Control-Allow-Origin': '*'\n    }\n  });\n}\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTH<PERSON>_EXPORTS from \"/Users/<USER>/Documents/augment-projects/elgatoai-bot/src/index.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"/Users/<USER>/.nvm/versions/node/v23.11.0/lib/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"/Users/<USER>/.nvm/versions/node/v23.11.0/lib/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"/Users/<USER>/Documents/augment-projects/elgatoai-bot/src/index.js\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"/Users/<USER>/Documents/augment-projects/elgatoai-bot/.wrangler/tmp/bundle-oNEGZA/middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"/Users/<USER>/.nvm/versions/node/v23.11.0/lib/node_modules/wrangler/templates/middleware/common.ts\";\nimport type { WorkerEntrypointConstructor } from \"/Users/<USER>/Documents/augment-projects/elgatoai-bot/.wrangler/tmp/bundle-oNEGZA/middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"/Users/<USER>/Documents/augment-projects/elgatoai-bot/.wrangler/tmp/bundle-oNEGZA/middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS,CAAC;AAAA;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;ACxBD,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAK,KAAK;AAE7B,QAAI,QAAQ,WAAW,WAAW;AAChC,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS;AAAA,UACP,+BAA+B;AAAA,UAC/B,gCAAgC;AAAA,UAChC,gCAAgC;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH;AAGA,QAAI,QAAQ,WAAW,QAAQ;AAC7B,aAAO,IAAI,SAAS,sBAAsB,EAAE,QAAQ,IAAI,CAAC;AAAA,IAC3D;AAEA,QAAI;AACF,YAAM,EAAE,MAAM,SAAS,UAAU,IAAI,MAAM,QAAQ,KAAK;AAGxD,UAAI,CAAC,SAAS;AACZ,eAAO,IAAI,SAAS,KAAK,UAAU;AAAA,UACjC,OAAO;AAAA,QACT,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,gBAAgB;AAAA,YAChB,+BAA+B;AAAA,UACjC;AAAA,QACF,CAAC;AAAA,MACH;AAGA,YAAM,cAAc,SAAS,QAAQ,YAAY,EAAE,SAAS,SAAS,IAAI,cAAc;AAGvF,YAAM,WAAW,eAAe,aAAa,OAAO;AAGpD,YAAM,aAAa,MAAM,IAAI,GAAG,IAAI,kCAAkC;AAAA,QACpE,QAAQ;AAAA,QACR,YAAY;AAAA,MACd,CAAC;AAGD,UAAI,iBAAiB,OAAO,GAAG;AAC7B,eAAO,MAAM,qBAAqB,SAAS,aAAa,WAAW,GAAG;AAAA,MACxE;AAGA,UAAI,kBAAkB,OAAO,GAAG;AAC9B,eAAO,MAAM,sBAAsB,SAAS,aAAa,WAAW,GAAG;AAAA,MACzE;AAGA,UAAI,iBAAiB,OAAO,GAAG;AAC7B,eAAO,MAAM,qBAAqB,SAAS,aAAa,YAAY,GAAG;AAAA,MACzE;AAGA,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,UAAU,WAAW,YAAY;AAAA,MACnC,CAAC,GAAG;AAAA,QACF,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,+BAA+B;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IAEH,SAAS,OAAO;AACd,cAAQ,MAAM,6BAA6B,KAAK;AAChD,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,+BAA+B;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAKA,SAAS,eAAe,MAAM,SAAS;AACrC,QAAM,WAAW;AAAA,IACf,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAAA,EACF;AAEA,QAAM,OAAO,SAAS,IAAI,KAAK,SAAS;AAExC,SAAO,gDAAgD,KAAK,IAAI;AAAA,mBAC/C,KAAK,QAAQ,iBAAiB,KAAK,SAAS;AAAA;AAAA,iBAE9C,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,oCAKY,KAAK,QAAQ;AAAA;AAAA;AAAA;AAIjD;AA9BS;AAmCT,SAAS,iBAAiB,SAAS;AACjC,QAAM,kBAAkB,CAAC,QAAQ,eAAe,YAAY,WAAW,gBAAgB,QAAQ,SAAS;AACxG,SAAO,gBAAgB,KAAK,aAAW,QAAQ,YAAY,EAAE,SAAS,OAAO,CAAC;AAChF;AAHS;AAQT,SAAS,kBAAkB,SAAS;AAClC,QAAM,mBAAmB,CAAC,YAAY,eAAe,WAAW,OAAO,YAAY,WAAW;AAC9F,SAAO,iBAAiB,KAAK,aAAW,QAAQ,YAAY,EAAE,SAAS,OAAO,CAAC;AACjF;AAHS;AAQT,SAAS,iBAAiB,SAAS;AACjC,QAAM,kBAAkB,CAAC,QAAQ,OAAO,OAAO,QAAQ,SAAS,YAAY,QAAQ,OAAO;AAC3F,SAAO,gBAAgB,KAAK,aAAW,QAAQ,YAAY,EAAE,SAAS,OAAO,CAAC;AAChF;AAHS;AAQT,eAAe,qBAAqB,SAAS,MAAM,WAAW,KAAK;AACjE,QAAM,gBAAgB,SAAS,cAAc,wBAAwB;AAErE,MAAI;AAEF,UAAM,mBAAmB,MAAM,MAAM,IAAI,uBAAuB;AAAA,MAC9D,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACnB,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAED,QAAI,iBAAiB,IAAI;AACvB,YAAM,cAAc,MAAM,iBAAiB,KAAK;AAChD,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,UAAU,iDAAiD,aAAa;AAAA,QACxE,WAAW,YAAY,aAAa;AAAA,MACtC,CAAC,GAAG;AAAA,QACF,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,+BAA+B;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACtC;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,kBAAkB,KAAK;AACrC,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,UAAU,qDAAqD,aAAa;AAAA,IAC9E,CAAC,GAAG;AAAA,MACF,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,+BAA+B;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AA3Ce;AAgDf,eAAe,sBAAsB,SAAS,MAAM,WAAW,KAAK;AAIlE,QAAM,WAAW,SAAS,cAAc,uCAAwC;AAEhF,SAAO,IAAI,SAAS,KAAK,UAAU;AAAA,IACjC,UAAU,uDAAuD,QAAQ;AAAA,EAC3E,CAAC,GAAG;AAAA,IACF,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,+BAA+B;AAAA,IACjC;AAAA,EACF,CAAC;AACH;AAde;AAmBf,eAAe,qBAAqB,SAAS,MAAM,YAAY,KAAK;AAClE,SAAO,IAAI,SAAS,KAAK,UAAU;AAAA,IACjC,UAAU,WAAW,WAAW;AAAA,EAClC,CAAC,GAAG;AAAA,IACF,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,+BAA+B;AAAA,IACjC;AAAA,EACF,CAAC;AACH;AATe;;;AC3Nf,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": []}