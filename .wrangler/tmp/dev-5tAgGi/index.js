var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-oNEGZA/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// src/index.js
var src_default = {
  async fetch(request, env, ctx) {
    if (request.method === "OPTIONS") {
      return new Response(null, {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type"
        }
      });
    }
    if (request.method !== "POST") {
      return new Response("Method not allowed", { status: 405 });
    }
    try {
      const { site, message, userEmail } = await request.json();
      if (!message) {
        return new Response(JSON.stringify({
          error: "Message is required"
        }), {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*"
          }
        });
      }
      const siteContext = site || (message.toLowerCase().includes("mustafa") ? "odmustafa" : "elgatoai");
      const aiPrompt = createAIPrompt(siteContext, message);
      const aiResponse = await env.AI.run("@cf/meta/llama-3.1-8b-instruct", {
        prompt: aiPrompt,
        max_tokens: 256
      });
      if (isBookingRequest(message)) {
        return await handleBookingRequest(message, siteContext, userEmail, env);
      }
      if (isBrochureRequest(message)) {
        return await handleBrochureRequest(message, siteContext, userEmail, env);
      }
      if (isGeneralInquiry(message)) {
        return await handleGeneralInquiry(message, siteContext, aiResponse, env);
      }
      return new Response(JSON.stringify({
        response: aiResponse.response || "I'm here to help you with booking appointments and answering questions about our services. How can I assist you today?"
      }), {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        }
      });
    } catch (error) {
      console.error("Error processing request:", error);
      return new Response(JSON.stringify({
        error: "Internal server error",
        response: "I'm sorry, I'm experiencing some technical difficulties. Please try again in a moment."
      }), {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        }
      });
    }
  }
};
function createAIPrompt(site, message) {
  const siteInfo = {
    elgatoai: {
      name: "ElgatoAI",
      services: "AI consulting, automation solutions, and digital transformation services",
      contact: "<EMAIL>",
      specialty: "helping businesses leverage AI technology"
    },
    odmustafa: {
      name: "Omar Mustafa",
      services: "personal AI consulting and technical advisory services",
      contact: "<EMAIL>",
      specialty: "AI strategy and implementation consulting"
    }
  };
  const info = siteInfo[site] || siteInfo.elgatoai;
  return `You are a professional booking assistant for ${info.name}. 
We specialize in ${info.services}, focusing on ${info.specialty}.

User message: "${message}"

Respond professionally and helpfully. If they want to:
- Book an appointment: Guide them through the booking process
- Request a brochure: Confirm we'll send them information
- Ask about services: Explain our ${info.services}
- General questions: Provide helpful, relevant information

Keep responses concise (under 100 words) and friendly. Always offer to help with booking or provide more information.`;
}
__name(createAIPrompt, "createAIPrompt");
function isBookingRequest(message) {
  const bookingKeywords = ["book", "appointment", "schedule", "meeting", "consultation", "call", "session"];
  return bookingKeywords.some((keyword) => message.toLowerCase().includes(keyword));
}
__name(isBookingRequest, "isBookingRequest");
function isBrochureRequest(message) {
  const brochureKeywords = ["brochure", "information", "details", "pdf", "document", "materials"];
  return brochureKeywords.some((keyword) => message.toLowerCase().includes(keyword));
}
__name(isBrochureRequest, "isBrochureRequest");
function isGeneralInquiry(message) {
  const inquiryKeywords = ["what", "how", "why", "when", "where", "services", "help", "about"];
  return inquiryKeywords.some((keyword) => message.toLowerCase().includes(keyword));
}
__name(isGeneralInquiry, "isGeneralInquiry");
async function handleBookingRequest(message, site, userEmail, env) {
  const calendarOwner = site === "odmustafa" ? "<EMAIL>" : "<EMAIL>";
  try {
    const calendarResponse = await fetch(env.CALENDAR_API_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        calendar: calendarOwner,
        message,
        userEmail,
        site
      })
    });
    if (calendarResponse.ok) {
      const bookingData = await calendarResponse.json();
      return new Response(JSON.stringify({
        response: `Great! I've initiated the booking process for ${calendarOwner}. You should receive a confirmation email shortly with available time slots. If you provided your email, we'll send you a calendar invite once confirmed.`,
        bookingId: bookingData.bookingId || null
      }), {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        }
      });
    } else {
      throw new Error("Calendar API error");
    }
  } catch (error) {
    console.error("Booking error:", error);
    return new Response(JSON.stringify({
      response: `I'd be happy to help you book an appointment with ${calendarOwner}. Please provide your email address and preferred time, and I'll get back to you with available slots within 24 hours.`
    }), {
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*"
      }
    });
  }
}
__name(handleBookingRequest, "handleBookingRequest");
async function handleBrochureRequest(message, site, userEmail, env) {
  const siteInfo = site === "odmustafa" ? "Omar Mustafa's consulting services" : "ElgatoAI's services";
  return new Response(JSON.stringify({
    response: `I'd be happy to send you detailed information about ${siteInfo}! Please provide your email address and I'll send you our comprehensive brochure with service details, case studies, and pricing information.`
  }), {
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*"
    }
  });
}
__name(handleBrochureRequest, "handleBrochureRequest");
async function handleGeneralInquiry(message, site, aiResponse, env) {
  return new Response(JSON.stringify({
    response: aiResponse.response + "\n\nWould you like to book a consultation or receive more detailed information about our services?"
  }), {
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*"
    }
  });
}
__name(handleGeneralInquiry, "handleGeneralInquiry");

// ../../../.nvm/versions/node/v23.11.0/lib/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// ../../../.nvm/versions/node/v23.11.0/lib/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-oNEGZA/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// ../../../.nvm/versions/node/v23.11.0/lib/node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-oNEGZA/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class ___Facade_ScheduledController__ {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  static {
    __name(this, "__Facade_ScheduledController__");
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof ___Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = /* @__PURE__ */ __name((request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    }, "#fetchDispatcher");
    #dispatcher = /* @__PURE__ */ __name((type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    }, "#dispatcher");
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
