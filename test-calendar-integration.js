#!/usr/bin/env node

/**
 * Test Calendar Integration
 * This simulates the worker's calendar booking functionality
 */

const CALENDAR_ENDPOINT = "https://script.google.com/macros/s/AKfycbyxEPgRE2sZY9wflgvL7My2Yjf6JAOCuSki6Gvu3EPxEX5qv9aco5sAJi5gtQTTaZ4/exec";

async function testCalendarBooking() {
  console.log('🧪 Testing Calendar Integration\n');
  console.log(`Calendar Endpoint: ${CALENDAR_ENDPOINT}\n`);

  const testCases = [
    {
      name: '<PERSON>',
      data: {
        calendar: '<EMAIL>',
        message: 'I would like to book a consultation about AI strategy and implementation',
        userEmail: '<EMAIL>',
        site: 'odmustafa'
      }
    },
    {
      name: 'ElgatoAI Booking',
      data: {
        calendar: '<EMAIL>',
        message: 'I need help with digital transformation and AI automation',
        userEmail: '<EMAIL>',
        site: 'elgatoai'
      }
    },
    {
      name: 'Booking without Email',
      data: {
        calendar: '<EMAIL>',
        message: 'I want to schedule a technical consultation',
        site: 'odmustafa'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`📋 Test: ${testCase.name}`);
    console.log('─'.repeat(50));
    
    try {
      console.log('Request Data:', JSON.stringify(testCase.data, null, 2));
      
      // Test with different methods to see what works
      console.log('\n🔄 Testing POST with JSON...');
      
      const response = await fetch(CALENDAR_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.data)
      });
      
      console.log(`Status: ${response.status} ${response.statusText}`);
      
      const contentType = response.headers.get('content-type');
      console.log(`Content-Type: ${contentType}`);
      
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        console.log('✅ JSON Response:');
        console.log(JSON.stringify(data, null, 2));
        
        if (data.success) {
          console.log('🎉 Booking successful!');
        } else {
          console.log('❌ Booking failed:', data.error || data.message);
        }
      } else {
        const text = await response.text();
        console.log('❌ Non-JSON Response (first 200 chars):');
        console.log(text.substring(0, 200));
        
        // Try alternative method - GET with parameters
        console.log('\n🔄 Trying GET with parameters...');
        const params = new URLSearchParams(testCase.data);
        const getResponse = await fetch(`${CALENDAR_ENDPOINT}?${params}`);
        
        if (getResponse.ok) {
          const getText = await getResponse.text();
          console.log('GET Response:', getText.substring(0, 200));
        }
      }
      
    } catch (error) {
      console.log('❌ Request failed:', error.message);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
  }
}

async function testWorkerLogic() {
  console.log('🤖 Testing Worker Logic Simulation\n');
  
  const testMessages = [
    'I want to book a consultation with Omar Mustafa',
    'Can you schedule a meeting for me?',
    'What services does ElgatoAI offer?',
    'I need to book an appointment about AI strategy',
    'Send me a brochure with your services'
  ];
  
  for (const message of testMessages) {
    console.log(`Message: "${message}"`);
    
    // Simulate worker logic
    const isBooking = isBookingRequest(message);
    const isBrochure = isBrochureRequest(message);
    const isGeneral = isGeneralInquiry(message);
    const site = message.toLowerCase().includes('mustafa') ? 'odmustafa' : 'elgatoai';
    
    console.log(`  - Booking request: ${isBooking}`);
    console.log(`  - Brochure request: ${isBrochure}`);
    console.log(`  - General inquiry: ${isGeneral}`);
    console.log(`  - Site context: ${site}`);
    
    if (isBooking) {
      console.log(`  → Would route to: ${site === 'odmustafa' ? '<EMAIL>' : '<EMAIL>'}`);
    }
    
    console.log('');
  }
}

// Helper functions (copied from worker)
function isBookingRequest(message) {
  const bookingKeywords = ['book', 'appointment', 'schedule', 'meeting', 'consultation', 'call', 'session'];
  return bookingKeywords.some(keyword => message.toLowerCase().includes(keyword));
}

function isBrochureRequest(message) {
  const brochureKeywords = ['brochure', 'information', 'details', 'pdf', 'document', 'materials'];
  return brochureKeywords.some(keyword => message.toLowerCase().includes(keyword));
}

function isGeneralInquiry(message) {
  const inquiryKeywords = ['what', 'how', 'why', 'when', 'where', 'services', 'help', 'about'];
  return inquiryKeywords.some(keyword => message.toLowerCase().includes(keyword));
}

async function main() {
  console.log('🚀 ElgatoAI Calendar Integration Test\n');
  
  try {
    // Test worker logic first
    await testWorkerLogic();
    
    console.log('─'.repeat(80) + '\n');
    
    // Test actual calendar integration
    await testCalendarBooking();
    
    console.log('🎯 Test Summary:');
    console.log('1. Worker logic correctly identifies booking requests');
    console.log('2. Site routing works (Omar vs ElgatoAI)');
    console.log('3. Calendar endpoint integration needs Google Apps Script fix');
    console.log('\n💡 Next Steps:');
    console.log('1. Update Google Apps Script with improved version');
    console.log('2. Test POST requests work properly');
    console.log('3. Deploy worker once authentication is fixed');
    console.log('4. Test end-to-end booking flow');
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

main();
