/**
 * FIXED Google Apps Script for Calendar Integration
 * This version works with Google Apps Script's API limitations
 * 
 * DEPLOYMENT STEPS:
 * 1. Go to script.google.com
 * 2. Create new project
 * 3. Paste this code
 * 4. Save (Ctrl+S)
 * 5. Deploy > New deployment
 * 6. Type: Web app
 * 7. Execute as: Me
 * 8. Who has access: Anyone
 * 9. Deploy
 * 10. Grant all permissions when prompted
 */

function doPost(e) {
  try {
    // Log the incoming request for debugging
    console.log('Received POST request');
    console.log('Request data:', e.postData ? e.postData.contents : 'No data');
    
    // Parse the request data
    let data = {};
    if (e.postData && e.postData.contents) {
      data = JSON.parse(e.postData.contents);
    }
    
    const { calendar, message, userEmail, site } = data;
    
    // Validate required fields
    if (!calendar || !message) {
      throw new Error('Missing required fields: calendar and message');
    }
    
    // For now, just return a success response without actually creating calendar events
    // This helps us test the basic deployment first
    const response = {
      success: true,
      message: 'Calendar endpoint is working! (Test mode - no actual booking created)',
      receivedData: {
        calendar: calendar,
        message: message,
        userEmail: userEmail || 'Not provided',
        site: site || 'Not specified'
      },
      timestamp: new Date().toISOString(),
      testMode: true
    };
    
    console.log('Sending response:', JSON.stringify(response));
    
    // Create the response with proper CORS headers
    const output = ContentService.createTextOutput(JSON.stringify(response));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
      
  } catch (error) {
    console.error('Error in doPost:', error);
    
    const errorResponse = {
      success: false,
      error: error.toString(),
      message: 'Failed to process request',
      timestamp: new Date().toISOString()
    };
    
    const output = ContentService.createTextOutput(JSON.stringify(errorResponse));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
  }
}

/**
 * Handle OPTIONS requests for CORS
 * Note: Google Apps Script has limited CORS support
 */
function doOptions(e) {
  console.log('Received OPTIONS request');
  
  const output = ContentService.createTextOutput('');
  output.setMimeType(ContentService.MimeType.TEXT);
  
  return output;
}

/**
 * Handle GET requests (for testing in browser)
 */
function doGet(e) {
  console.log('Received GET request');
  
  const response = {
    message: 'ElgatoAI Calendar API is running',
    status: 'OK',
    timestamp: new Date().toISOString(),
    note: 'Send POST requests to this endpoint with calendar booking data'
  };
  
  const output = ContentService.createTextOutput(JSON.stringify(response));
  output.setMimeType(ContentService.MimeType.JSON);
  
  return output;
}

/**
 * Test function you can run manually in the Apps Script editor
 */
function testEndpoint() {
  console.log('Running manual test...');
  
  const mockEvent = {
    postData: {
      contents: JSON.stringify({
        calendar: '<EMAIL>',
        message: 'Test message from manual test',
        userEmail: '<EMAIL>',
        site: 'test'
      })
    }
  };
  
  const result = doPost(mockEvent);
  console.log('Test result:', result.getContent());
  
  return result.getContent();
}

/**
 * Simple function to verify the script is working
 */
function hello() {
  console.log('Hello from Google Apps Script!');
  return 'Hello from Google Apps Script!';
}

/**
 * FULL CALENDAR INTEGRATION VERSION
 * Replace doPost with this function once the basic version works
 */
function doPostWithCalendar(e) {
  try {
    const data = JSON.parse(e.postData.contents);
    const { calendar, message, userEmail, site } = data;
    
    if (!calendar || !message) {
      throw new Error('Missing required fields: calendar and message');
    }
    
    // Determine which calendar to use
    const calendarId = calendar === '<EMAIL>' ? 
      '<EMAIL>' : '<EMAIL>';
    
    // Get the calendar
    const cal = CalendarApp.getCalendarById(calendarId);
    
    if (!cal) {
      throw new Error(`Calendar not found: ${calendarId}`);
    }
    
    // Find next available slot (simplified)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0); // 2 PM tomorrow
    
    // Skip weekends
    while (tomorrow.getDay() === 0 || tomorrow.getDay() === 6) {
      tomorrow.setDate(tomorrow.getDate() + 1);
    }
    
    const endTime = new Date(tomorrow.getTime() + (60 * 60 * 1000)); // 1 hour later
    
    // Create calendar event
    const title = `Consultation - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
    const description = `Booking Request: ${message}\n\nUser Email: ${userEmail || 'Not provided'}\nSite: ${site}\nBooked via: ElgatoAI Chat Bot`;
    
    const event = cal.createEvent(title, tomorrow, endTime, {
      description: description,
      guests: userEmail ? [userEmail] : [],
      sendInvites: !!userEmail,
      location: 'Video Call (link will be provided)'
    });
    
    // Send confirmation email if user email provided
    if (userEmail && isValidEmail(userEmail)) {
      const subject = `Appointment Confirmed - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
      const body = `Dear Client,

Thank you for booking a consultation!

Your appointment has been scheduled for:
📅 Date: ${tomorrow.toDateString()}
🕐 Time: ${tomorrow.toLocaleTimeString()}
⏱️ Duration: 1 hour

We look forward to speaking with you!

Best regards,
${site === 'odmustafa' ? 'Omar Mustafa' : 'The ElgatoAI Team'}`;
      
      GmailApp.sendEmail(userEmail, subject, body);
    }
    
    const response = {
      success: true,
      bookingId: event.getId(),
      dateTime: tomorrow.toISOString(),
      message: 'Appointment scheduled successfully',
      calendarOwner: calendar
    };
    
    const output = ContentService.createTextOutput(JSON.stringify(response));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
      
  } catch (error) {
    const errorResponse = {
      success: false,
      error: error.toString(),
      message: 'Failed to schedule appointment'
    };
    
    const output = ContentService.createTextOutput(JSON.stringify(errorResponse));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
  }
}

/**
 * Validate email address
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
