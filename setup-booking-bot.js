#!/usr/bin/env node

/**
 * ElgatoAI Booking Bot Setup Script
 * This script helps set up the complete booking bot system
 */

import { execSync } from 'child_process';
import fs from 'fs';
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

console.log('🚀 ElgatoAI Booking Bot Setup\n');
console.log('This script will help you set up the complete booking bot system.\n');

async function main() {
  try {
    // Step 1: Check prerequisites
    console.log('📋 Step 1: Checking prerequisites...');
    
    if (!fs.existsSync('wrangler.toml')) {
      console.log('❌ wrangler.toml not found');
      process.exit(1);
    }
    
    if (!fs.existsSync('src/index.js')) {
      console.log('❌ src/index.js not found');
      process.exit(1);
    }
    
    console.log('✅ All required files found');
    
    // Step 2: Check Wrangler authentication
    console.log('\n🔐 Step 2: Checking Wrangler authentication...');
    try {
      const whoami = execSync('npx wrangler whoami', { encoding: 'utf8' });
      if (whoami.includes('You are logged in')) {
        console.log('✅ Wrangler authentication successful');
      } else {
        console.log('❌ Please run "npx wrangler login" first');
        process.exit(1);
      }
    } catch (error) {
      console.log('❌ Wrangler authentication failed. Please run "npx wrangler login"');
      process.exit(1);
    }
    
    // Step 3: Enable Workers AI
    console.log('\n🤖 Step 3: Workers AI Setup');
    console.log('Please ensure you have enabled Workers AI in your Cloudflare dashboard:');
    console.log('1. Go to https://dash.cloudflare.com/');
    console.log('2. Navigate to Workers & Pages > AI');
    console.log('3. Enable Workers AI');
    
    const aiEnabled = await question('Have you enabled Workers AI? (y/n): ');
    if (aiEnabled.toLowerCase() !== 'y') {
      console.log('Please enable Workers AI first, then run this script again.');
      process.exit(1);
    }
    
    // Step 4: Configure environment variables
    console.log('\n⚙️ Step 4: Environment Configuration');
    
    const gatewayId = await question('Enter your AI Gateway ID (optional, press Enter to skip): ');
    const apiToken = await question('Enter your API token (optional, press Enter to skip): ');
    const calendarEndpoint = await question('Enter your calendar API endpoint URL (or press Enter for placeholder): ');
    
    // Update wrangler.toml with user inputs
    let wranglerConfig = fs.readFileSync('wrangler.toml', 'utf8');
    
    if (gatewayId) {
      wranglerConfig = wranglerConfig.replace('GATEWAY_ID = "your_gateway_id_here"', `GATEWAY_ID = "${gatewayId}"`);
    }
    
    if (apiToken) {
      wranglerConfig = wranglerConfig.replace('API_TOKEN = "your_api_token_here"', `API_TOKEN = "${apiToken}"`);
    }
    
    if (calendarEndpoint) {
      wranglerConfig = wranglerConfig.replace('CALENDAR_API_ENDPOINT = "https://your-calendar-api-endpoint.com"', `CALENDAR_API_ENDPOINT = "${calendarEndpoint}"`);
    }
    
    fs.writeFileSync('wrangler.toml', wranglerConfig);
    console.log('✅ Configuration updated');
    
    // Step 5: Create KV namespace
    console.log('\n🗄️ Step 5: Creating KV namespace for chat sessions...');
    try {
      const kvOutput = execSync('npx wrangler kv:namespace create CHAT_SESSIONS', { encoding: 'utf8' });
      console.log('✅ KV namespace created');
      console.log('Please update your wrangler.toml with the KV namespace ID from the output above.');
    } catch (error) {
      console.log('⚠️ KV namespace creation failed (may already exist)');
    }
    
    // Step 6: Deploy the worker
    console.log('\n🚀 Step 6: Deploying the worker...');
    const deploy = await question('Deploy the worker now? (y/n): ');
    
    if (deploy.toLowerCase() === 'y') {
      try {
        execSync('npx wrangler deploy', { stdio: 'inherit' });
        console.log('✅ Worker deployed successfully!');
      } catch (error) {
        console.log('❌ Deployment failed:', error.message);
      }
    }
    
    // Step 7: Generate embed code
    console.log('\n📝 Step 7: Generating embed code...');
    
    const workerUrl = await question('Enter your worker URL (e.g., https://elgatoai-bot.your-subdomain.workers.dev): ');
    
    if (workerUrl) {
      // Update embed widget with actual worker URL
      let embedCode = fs.readFileSync('embed-widget.js', 'utf8');
      embedCode = embedCode.replace('https://elgatoai-bot.your-subdomain.workers.dev', workerUrl);
      fs.writeFileSync('embed-widget.js', embedCode);
      
      // Update chat widget HTML
      let chatWidget = fs.readFileSync('chat-widget.html', 'utf8');
      chatWidget = chatWidget.replace('https://elgatoai-bot.your-subdomain.workers.dev', workerUrl);
      fs.writeFileSync('chat-widget.html', chatWidget);
      
      console.log('✅ Embed code updated with your worker URL');
    }
    
    // Step 8: Next steps
    console.log('\n🎉 Setup Complete!');
    console.log('\n📋 Next Steps:');
    console.log('1. Set up Google Calendar integration (see src/calendar-service.js for examples)');
    console.log('2. Test your worker at:', workerUrl || 'your-worker-url');
    console.log('3. Embed the chat widget on your website using embed-widget.js');
    console.log('4. Customize the AI prompts and responses in src/index.js');
    
    console.log('\n🔧 Available Commands:');
    console.log('- npm run dev: Start local development server');
    console.log('- npm run deploy: Deploy to Cloudflare');
    console.log('- npm run preview: Preview locally');
    
    console.log('\n📚 Documentation:');
    console.log('- Chat Widget: chat-widget.html (standalone)');
    console.log('- Embed Script: embed-widget.js (for websites)');
    console.log('- Calendar Integration: src/calendar-service.js');
    console.log('- Main Worker: src/index.js');
    
  } catch (error) {
    console.error('Setup failed:', error);
  } finally {
    rl.close();
  }
}

main();
