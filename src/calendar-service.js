/**
 * Google Calendar Integration Service
 * This would typically be deployed as a separate Cloud Run service or Google Apps Script
 * For now, this is the structure you would implement
 */

/**
 * Google Apps Script version for Calendar Integration
 * Deploy this as a Google Apps Script web app
 */

// Google Apps Script Code (calendar-integration.gs)
const CALENDAR_INTEGRATION_CODE = `
function doPost(e) {
  try {
    const data = JSON.parse(e.postData.contents);
    const { calendar, message, userEmail, site } = data;
    
    // Determine which calendar to use based on the email
    const calendarId = calendar === '<EMAIL>' ? 
      '<EMAIL>' : '<EMAIL>';
    
    // Get the calendar
    const cal = CalendarApp.getCalendarById(calendarId);
    
    if (!cal) {
      throw new Error('Calendar not found');
    }
    
    // Find next available slot (next business day, 2 PM)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Set to 2 PM
    tomorrow.setHours(14, 0, 0, 0);
    
    // Skip weekends
    while (tomorrow.getDay() === 0 || tomorrow.getDay() === 6) {
      tomorrow.setDate(tomorrow.getDate() + 1);
    }
    
    // Check for conflicts and find next available slot
    const endTime = new Date(tomorrow.getTime() + (60 * 60 * 1000)); // 1 hour meeting
    const events = cal.getEvents(tomorrow, endTime);
    
    while (events.length > 0) {
      tomorrow.setHours(tomorrow.getHours() + 1);
      endTime.setHours(endTime.getHours() + 1);
      events = cal.getEvents(tomorrow, endTime);
    }
    
    // Create calendar event
    const event = cal.createEvent(
      \`Consultation - \${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}\`,
      tomorrow,
      endTime,
      {
        description: \`Booking request: \${message}\\n\\nUser email: \${userEmail || 'Not provided'}\`,
        guests: userEmail ? [userEmail] : [],
        sendInvites: !!userEmail
      }
    );
    
    // Send confirmation email if user email provided
    if (userEmail) {
      const subject = \`Appointment Confirmed - \${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}\`;
      const body = \`
Dear Client,

Your appointment has been scheduled for:
Date: \${tomorrow.toDateString()}
Time: \${tomorrow.toLocaleTimeString()}

Meeting Details:
- Duration: 1 hour
- Type: Consultation
- Calendar: \${calendar}

You should receive a calendar invitation shortly.

If you need to reschedule or cancel, please reply to this email.

Best regards,
\${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI Team'}
      \`;
      
      GmailApp.sendEmail(userEmail, subject, body);
    }
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        bookingId: event.getId(),
        dateTime: tomorrow.toISOString(),
        message: 'Appointment scheduled successfully'
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('Calendar booking error:', error);
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

// Helper function to get OAuth token (you'll need to set this up)
function getOAuthToken(email) {
  // This would return the stored OAuth token for the specific email
  // You'll need to implement OAuth flow for each calendar owner
  const tokens = {
    '<EMAIL>': 'oauth_token_for_omar',
    '<EMAIL>': 'oauth_token_for_elgatoai'
  };
  return tokens[email];
}
`;

/**
 * Alternative: Cloud Run / Express.js implementation
 */
const EXPRESS_IMPLEMENTATION = `
const express = require('express');
const { google } = require('googleapis');
const nodemailer = require('nodemailer');

const app = express();
app.use(express.json());

// OAuth2 clients for each calendar
const oauth2Clients = {
  '<EMAIL>': new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.REDIRECT_URI
  ),
  '<EMAIL>': new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.REDIRECT_URI
  )
};

// Set credentials (you'll need to implement OAuth flow)
oauth2Clients['<EMAIL>'].setCredentials({
  refresh_token: process.env.OMAR_REFRESH_TOKEN
});

oauth2Clients['<EMAIL>'].setCredentials({
  refresh_token: process.env.ELGATOAI_REFRESH_TOKEN
});

app.post('/book', async (req, res) => {
  try {
    const { calendar, message, userEmail, site } = req.body;
    
    const auth = oauth2Clients[calendar];
    const calendarApi = google.calendar({ version: 'v3', auth });
    
    // Find next available slot
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0);
    
    // Skip weekends
    while (tomorrow.getDay() === 0 || tomorrow.getDay() === 6) {
      tomorrow.setDate(tomorrow.getDate() + 1);
    }
    
    const endTime = new Date(tomorrow.getTime() + (60 * 60 * 1000));
    
    // Check for conflicts
    const busyResponse = await calendarApi.freebusy.query({
      requestBody: {
        timeMin: tomorrow.toISOString(),
        timeMax: endTime.toISOString(),
        items: [{ id: calendar }]
      }
    });
    
    // Find available slot (simplified logic)
    while (busyResponse.data.calendars[calendar].busy.length > 0) {
      tomorrow.setHours(tomorrow.getHours() + 1);
      endTime.setHours(endTime.getHours() + 1);
    }
    
    // Create event
    const event = await calendarApi.events.insert({
      calendarId: calendar,
      requestBody: {
        summary: \`Consultation - \${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}\`,
        start: { dateTime: tomorrow.toISOString() },
        end: { dateTime: endTime.toISOString() },
        description: \`Booking request: \${message}\\n\\nUser email: \${userEmail || 'Not provided'}\`,
        attendees: userEmail ? [{ email: userEmail }] : []
      }
    });
    
    // Send confirmation email
    if (userEmail) {
      // Implement email sending logic here
    }
    
    res.json({
      success: true,
      bookingId: event.data.id,
      dateTime: tomorrow.toISOString(),
      message: 'Appointment scheduled successfully'
    });
    
  } catch (error) {
    console.error('Booking error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

app.listen(process.env.PORT || 8080);
`;

// Export the code templates for reference
export { CALENDAR_INTEGRATION_CODE, EXPRESS_IMPLEMENTATION };
