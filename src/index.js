/**
 * ElgatoAI Booking Bot - Cloudflare Worker
 * Handles chat interactions, booking requests, and multi-site routing
 */

export default {
  async fetch(request, env, ctx) {
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      });
    }

    // Only handle POST requests for chat
    if (request.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    try {
      const { site, message, userEmail } = await request.json();

      // Validate input
      if (!message) {
        return new Response(JSON.stringify({
          error: 'Message is required'
        }), { 
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }

      // Determine site context
      const siteContext = site || (message.toLowerCase().includes('mustafa') ? 'odmustafa' : 'elgatoai');
      
      // Create AI prompt based on site and message
      const aiPrompt = createAIPrompt(siteContext, message);

      // Get AI response using Workers AI
      const aiResponse = await env.AI.run('@cf/meta/llama-3.1-8b-instruct', {
        prompt: aiPrompt,
        max_tokens: 256
      });

      // Handle booking requests
      if (isBookingRequest(message)) {
        return await handleBookingRequest(message, siteContext, userEmail, env);
      }

      // Handle brochure requests
      if (isBrochureRequest(message)) {
        return await handleBrochureRequest(message, siteContext, userEmail, env);
      }

      // Handle general inquiries
      if (isGeneralInquiry(message)) {
        return await handleGeneralInquiry(message, siteContext, aiResponse, env);
      }

      // Default AI response
      return new Response(JSON.stringify({ 
        response: aiResponse.response || "I'm here to help you with booking appointments and answering questions about our services. How can I assist you today?"
      }), {
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });

    } catch (error) {
      console.error('Error processing request:', error);
      return new Response(JSON.stringify({
        error: 'Internal server error',
        response: "I'm sorry, I'm experiencing some technical difficulties. Please try again in a moment."
      }), { 
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
};

/**
 * Create AI prompt based on site context and user message
 */
function createAIPrompt(site, message) {
  const siteInfo = {
    elgatoai: {
      name: "ElgatoAI",
      services: "AI consulting, automation solutions, and digital transformation services",
      contact: "<EMAIL>",
      specialty: "helping businesses leverage AI technology"
    },
    odmustafa: {
      name: "Omar Mustafa",
      services: "personal AI consulting and technical advisory services",
      contact: "<EMAIL>", 
      specialty: "AI strategy and implementation consulting"
    }
  };

  const info = siteInfo[site] || siteInfo.elgatoai;

  return `You are a professional booking assistant for ${info.name}. 
We specialize in ${info.services}, focusing on ${info.specialty}.

User message: "${message}"

Respond professionally and helpfully. If they want to:
- Book an appointment: Guide them through the booking process
- Request a brochure: Confirm we'll send them information
- Ask about services: Explain our ${info.services}
- General questions: Provide helpful, relevant information

Keep responses concise (under 100 words) and friendly. Always offer to help with booking or provide more information.`;
}

/**
 * Check if message is a booking request
 */
function isBookingRequest(message) {
  const bookingKeywords = ['book', 'appointment', 'schedule', 'meeting', 'consultation', 'call', 'session'];
  return bookingKeywords.some(keyword => message.toLowerCase().includes(keyword));
}

/**
 * Check if message is a brochure request
 */
function isBrochureRequest(message) {
  const brochureKeywords = ['brochure', 'information', 'details', 'pdf', 'document', 'materials'];
  return brochureKeywords.some(keyword => message.toLowerCase().includes(keyword));
}

/**
 * Check if message is a general inquiry
 */
function isGeneralInquiry(message) {
  const inquiryKeywords = ['what', 'how', 'why', 'when', 'where', 'services', 'help', 'about'];
  return inquiryKeywords.some(keyword => message.toLowerCase().includes(keyword));
}

/**
 * Handle booking requests
 */
async function handleBookingRequest(message, site, userEmail, env) {
  const calendarOwner = site === 'odmustafa' ? '<EMAIL>' : '<EMAIL>';
  
  try {
    // Call calendar API to book appointment
    const calendarResponse = await fetch(`${env.CALENDAR_API_ENDPOINT}/book`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${env.API_TOKEN}`
      },
      body: JSON.stringify({ 
        calendar: calendarOwner, 
        message,
        userEmail,
        site
      })
    });

    if (calendarResponse.ok) {
      const bookingData = await calendarResponse.json();
      return new Response(JSON.stringify({
        response: `Great! I've initiated the booking process for ${calendarOwner}. You should receive a confirmation email shortly with available time slots. If you provided your email, we'll send you a calendar invite once confirmed.`,
        bookingId: bookingData.bookingId || null
      }), { 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      });
    } else {
      throw new Error('Calendar API error');
    }
  } catch (error) {
    console.error('Booking error:', error);
    return new Response(JSON.stringify({
      response: `I'd be happy to help you book an appointment with ${calendarOwner}. Please provide your email address and preferred time, and I'll get back to you with available slots within 24 hours.`
    }), { 
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      } 
    });
  }
}

/**
 * Handle brochure requests
 */
async function handleBrochureRequest(message, site, userEmail, env) {
  // In a real implementation, you would integrate with an email service
  // For now, we'll provide a helpful response
  
  const siteInfo = site === 'odmustafa' ? 'Omar Mustafa\'s consulting services' : 'ElgatoAI\'s services';
  
  return new Response(JSON.stringify({
    response: `I'd be happy to send you detailed information about ${siteInfo}! Please provide your email address and I'll send you our comprehensive brochure with service details, case studies, and pricing information.`
  }), { 
    headers: { 
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    } 
  });
}

/**
 * Handle general inquiries
 */
async function handleGeneralInquiry(message, site, aiResponse, env) {
  return new Response(JSON.stringify({ 
    response: aiResponse.response + "\n\nWould you like to book a consultation or receive more detailed information about our services?"
  }), {
    headers: { 
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    }
  });
}
