# ElgatoAI Bot - Cloudflare Workers Booking Bot

This project implements a complete AI-powered booking chatbot for www.elgatoai.com using Cloudflare Workers, Workers AI, and Google Calendar integration. The bot can handle multi-site routing, dynamic scheduling, and automated email confirmations.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- Cloudflare account with Workers AI enabled
- Google account for calendar integration
- Wrangler CLI access

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Login to Cloudflare:**
   ```bash
   npx wrangler login
   ```

3. **Run the booking bot setup:**
   ```bash
   npm run setup-booking-bot
   ```

## 🎯 Features

- **AI-Powered Chat**: Uses Cloudflare Workers AI (Llama 3.1) for intelligent responses
- **Multi-Site Routing**: Automatically routes bookings to different calendars based on context
- **Google Calendar Integration**: Automatically schedules appointments and sends confirmations
- **Email Automation**: Sends booking confirmations and notifications
- **Embeddable Widget**: Easy-to-embed chat widget for any website
- **Real-time Responses**: Fast edge-based processing with Cloudflare Workers

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Website       │    │  Cloudflare      │    │  Google         │
│   Chat Widget   │───▶│  Worker + AI     │───▶│  Calendar API   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │  Email           │
                       │  Notifications   │
                       └──────────────────┘
```

## 🔧 Setup Process

### Step 1: Enable Cloudflare Services

1. Go to your [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. Navigate to **Workers & Pages > AI**
3. Enable **Workers AI**
4. (Optional) Set up **AI Gateway** for monitoring

### Step 2: Configure Google Calendar Integration

1. Go to [Google Apps Script](https://script.google.com/)
2. Create a new project
3. Copy the code from `google-apps-script-calendar.gs`
4. Deploy as a web app with "Anyone" execute permissions
5. Copy the web app URL for use as `CALENDAR_API_ENDPOINT`

### Step 3: Deploy the Worker

1. **Run the automated setup:**
   ```bash
   npm run setup-booking-bot
   ```

2. **Or manually configure and deploy:**
   ```bash
   # Update wrangler.toml with your settings
   npm run deploy
   ```

## 🛠️ Available Scripts

- `npm run deploy` - Deploy the Worker to Cloudflare
- `npm run dev` - Start local development server
- `npm run setup-workers-mcp` - Set up Workers MCP integration
- `npm run setup-cloudflare-mcp` - Set up Cloudflare MCP server

## 📁 Project Structure

```
elgatoai-bot/
├── worker-mcp-example.js     # Example Worker with MCP functions
├── wrangler.toml            # Cloudflare Worker configuration
├── claude-mcp-config.json   # Claude Desktop MCP configuration
├── setup-workers-mcp.js     # Workers MCP setup script
└── README.md               # This file
```

## 🎯 Example Functions

The example Worker includes these MCP functions:

- `getGreeting(name)` - Get a personalized greeting
- `getRandomNumber(min, max)` - Generate random numbers
- `getServerInfo()` - Get server timestamp and location
- `echo(message)` - Echo messages back

## 🔍 Testing

1. **Test the Cloudflare MCP Server:**
   - Open Claude Desktop
   - Look for the 🔨 tool icon
   - Try commands like "List my Cloudflare Workers" or "Show my KV namespaces"

2. **Test the Workers MCP:**
   - Deploy your Worker first: `npm run deploy`
   - Configure Claude Desktop to connect to your Worker
   - Try calling the custom functions

## 📚 What You Can Do

### With Cloudflare MCP Server:
- Manage Workers, KV, R2, D1 databases
- Deploy and update Workers
- Manage DNS and domains
- Access analytics and logs
- Manage Durable Objects and Queues

### With Workers MCP:
- Create custom edge functions
- Process data at Cloudflare's edge
- Integrate with external APIs
- Build custom AI tools and automations

## 🔧 Troubleshooting

1. **Authentication Issues:**
   ```bash
   npx wrangler logout
   npx wrangler login
   ```

2. **Claude Desktop Not Detecting MCP:**
   - Restart Claude Desktop after configuration changes
   - Check the configuration file location
   - Verify the MCP server is running

3. **Worker Deployment Issues:**
   - Check your account ID in `wrangler.toml`
   - Ensure you have the necessary permissions
   - Try `npx wrangler whoami` to verify authentication

## 📖 Next Steps

1. Customize the `worker-mcp-example.js` with your own functions
2. Add environment variables and bindings in `wrangler.toml`
3. Explore the Cloudflare MCP server capabilities
4. Build custom AI workflows with Claude Desktop

## 🔗 Resources

- [Cloudflare MCP Server Documentation](https://github.com/cloudflare/mcp-server-cloudflare)
- [Workers MCP Documentation](https://github.com/cloudflare/workers-mcp)
- [Model Context Protocol](https://modelcontextprotocol.io/)
- [Cloudflare Workers Documentation](https://developers.cloudflare.com/workers/)
