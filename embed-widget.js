/**
 * ElgatoAI Chat Widget - Embeddable Script
 * Add this script to any website to embed the chat widget
 */

(function() {
    'use strict';
    
    // Configuration
    const WORKER_URL = 'https://elgatoai-bot.your-subdomain.workers.dev';
    
    // Prevent multiple initializations
    if (window.ElgatoAIChatWidget) {
        return;
    }
    
    // Widget HTML template
    const widgetHTML = `
        <div id="elgatoai-chatbox" style="
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            z-index: 10000;
            transition: all 0.3s ease;
        ">
            <div class="chat-header" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px;
                border-radius: 10px 10px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: pointer;
            ">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600;">💬 ElgatoAI Assistant</h3>
                <button id="chat-toggle" style="
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 0;
                    width: 24px;
                    height: 24px;
                ">−</button>
            </div>
            <div id="chat-body" style="
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            ">
                <div id="elgatoai-messages" style="
                    flex: 1;
                    padding: 15px;
                    overflow-y: auto;
                    background: #f8f9fa;
                ">
                    <div class="message bot" style="
                        margin-bottom: 12px;
                        padding: 10px 12px;
                        border-radius: 18px;
                        max-width: 80%;
                        word-wrap: break-word;
                        line-height: 1.4;
                        font-size: 14px;
                        background: white;
                        color: #333;
                        border: 1px solid #e1e5e9;
                    ">
                        👋 Hi! I'm your ElgatoAI assistant. I can help you book consultations, answer questions about our services, or send you detailed information. How can I help you today?
                    </div>
                </div>
                <div class="chat-input-container" style="
                    padding: 15px;
                    background: white;
                    border-top: 1px solid #e1e5e9;
                    border-radius: 0 0 10px 10px;
                ">
                    <div style="display: flex; gap: 8px;">
                        <input 
                            id="elgatoai-msg" 
                            placeholder="Type your message..."
                            style="
                                flex: 1;
                                padding: 10px 12px;
                                border: 1px solid #ddd;
                                border-radius: 20px;
                                outline: none;
                                font-size: 14px;
                                font-family: inherit;
                            "
                        />
                        <button id="send-button" style="
                            background: #667eea;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 40px;
                            height: 40px;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">➤</button>
                    </div>
                    <input 
                        id="user-email" 
                        placeholder="Your email (optional, for booking confirmations)"
                        style="
                            width: 100%;
                            padding: 8px 12px;
                            border: 1px solid #ddd;
                            border-radius: 15px;
                            margin-top: 8px;
                            font-size: 13px;
                            outline: none;
                            display: none;
                            box-sizing: border-box;
                        "
                    />
                </div>
            </div>
        </div>
    `;
    
    // Widget class
    class ElgatoAIChatWidget {
        constructor() {
            this.isMinimized = false;
            this.userEmail = '';
            this.chatSite = this.detectSite();
            this.init();
        }
        
        detectSite() {
            const hostname = window.location.hostname;
            return hostname.includes('mustafa') ? 'odmustafa' : 'elgatoai';
        }
        
        init() {
            // Create widget container
            const container = document.createElement('div');
            container.innerHTML = widgetHTML;
            document.body.appendChild(container);
            
            // Bind events
            this.bindEvents();
            
            // Load chat history
            this.loadChatHistory();
        }
        
        bindEvents() {
            const chatHeader = document.querySelector('.chat-header');
            const sendButton = document.getElementById('send-button');
            const msgInput = document.getElementById('elgatoai-msg');
            
            chatHeader.addEventListener('click', () => this.toggleChat());
            sendButton.addEventListener('click', () => this.sendMessage());
            msgInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.sendMessage();
            });
        }
        
        toggleChat() {
            const chatbox = document.getElementById('elgatoai-chatbox');
            const chatBody = document.getElementById('chat-body');
            const toggle = document.getElementById('chat-toggle');
            
            this.isMinimized = !this.isMinimized;
            
            if (this.isMinimized) {
                chatbox.style.height = '60px';
                chatbox.style.width = '300px';
                chatBody.style.display = 'none';
                toggle.textContent = '+';
            } else {
                chatbox.style.height = '500px';
                chatbox.style.width = '350px';
                chatBody.style.display = 'flex';
                toggle.textContent = '−';
            }
        }
        
        async sendMessage() {
            const msgInput = document.getElementById('elgatoai-msg');
            const emailInput = document.getElementById('user-email');
            const sendButton = document.getElementById('send-button');
            const msg = msgInput.value.trim();
            
            if (!msg) return;
            
            // Get user email if provided
            if (emailInput.style.display !== 'none') {
                this.userEmail = emailInput.value.trim();
            }
            
            // Add user message
            this.addMessage(msg, 'user');
            msgInput.value = '';
            
            // Disable send button
            sendButton.disabled = true;
            sendButton.style.background = '#ccc';
            
            try {
                const response = await fetch(WORKER_URL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: msg,
                        site: this.chatSite,
                        userEmail: this.userEmail
                    })
                });
                
                const data = await response.json();
                this.addMessage(data.response || data.error || 'Sorry, I encountered an error.', 'bot');
                
                // Show email input for booking/brochure requests
                if (!this.userEmail && (msg.toLowerCase().includes('book') || msg.toLowerCase().includes('brochure'))) {
                    this.showEmailInput();
                }
                
                this.saveChatHistory();
                
            } catch (error) {
                console.error('Chat error:', error);
                this.addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'bot');
            } finally {
                sendButton.disabled = false;
                sendButton.style.background = '#667eea';
            }
        }
        
        addMessage(text, sender) {
            const messagesDiv = document.getElementById('elgatoai-messages');
            const messageDiv = document.createElement('div');
            
            const isUser = sender === 'user';
            messageDiv.style.cssText = `
                margin-bottom: 12px;
                padding: 10px 12px;
                border-radius: 18px;
                max-width: 80%;
                word-wrap: break-word;
                line-height: 1.4;
                font-size: 14px;
                ${isUser ? `
                    background: #667eea;
                    color: white;
                    margin-left: auto;
                    text-align: right;
                ` : `
                    background: white;
                    color: #333;
                    border: 1px solid #e1e5e9;
                `}
            `;
            
            messageDiv.textContent = text;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        showEmailInput() {
            const emailInput = document.getElementById('user-email');
            emailInput.style.display = 'block';
        }
        
        saveChatHistory() {
            const messages = Array.from(document.querySelectorAll('#elgatoai-messages > div')).map(msg => ({
                text: msg.textContent,
                sender: msg.style.background.includes('667eea') ? 'user' : 'bot'
            }));
            localStorage.setItem('elgatoai-chat-history', JSON.stringify(messages));
        }
        
        loadChatHistory() {
            const history = localStorage.getItem('elgatoai-chat-history');
            if (history) {
                const messages = JSON.parse(history);
                const messagesDiv = document.getElementById('elgatoai-messages');
                messagesDiv.innerHTML = '';
                
                messages.forEach(msg => {
                    this.addMessage(msg.text, msg.sender);
                });
            }
        }
    }
    
    // Initialize widget when DOM is ready
    function initWidget() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                window.ElgatoAIChatWidget = new ElgatoAIChatWidget();
            });
        } else {
            window.ElgatoAIChatWidget = new ElgatoAIChatWidget();
        }
    }
    
    initWidget();
})();
