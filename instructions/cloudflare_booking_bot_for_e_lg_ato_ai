### eLgAtoAi: Cloudflare Workers-Based Booking Bot Setup

#### Goal
Build a booking chatbot for www.elgatoai.com using Cloudflare Workers and Workers AI, with dynamic scheduling, multi-site logic, and calendar/email automation.

---

## Step-by-Step Setup (Cloudflare Workers)

### 1. Enable Cloudflare Services
- Enable **Workers AI** and **AI Gateway** in your Cloudflare dashboard.
- Note your `account_id`, `gateway_id`, and API token.

---

### 2. Scaffold Worker Project
- Install Wrangler CLI (`npm install -g wrangler`)
- Run `wrangler init elgatoai-bot`
- Update `wrangler.toml` with:
  ```toml
  name = "elgatoai-bot"
  main = "src/index.js"
  compatibility_date = "2024-06-01"

  [ai]
  binding = "AI"

  [vars]
  GATEWAY_ID = "<your_gateway_id>"
  API_TOKEN = "<your_token>"
  ```

---

### 3. Worker Code (src/index.js)
```javascript
export default {
  async fetch(request, env) {
    const { site, message } = await request.json();

    const aiResponse = await env.AI.run('@cf/meta/llama-3.1-8b-instruct', {
      prompt: `You are the booking assistant for ${site}. ${message}`
    });

    if (message.toLowerCase().includes('book')) {
      const calendarOwner = site === 'odmustafa' ? '<EMAIL>' : '<EMAIL>';
      await fetch('https://your-calendar-api-endpoint/book', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ calendar: calendarOwner, message })
      });
      return new Response(JSON.stringify({
        response: `Booking confirmed for ${calendarOwner}. You will receive an email.`
      }), { headers: { 'Content-Type': 'application/json' } });
    }

    if (message.toLowerCase().includes('brochure')) {
      return new Response(JSON.stringify({
        response: `A brochure has been sent to your email.`
      }), { headers: { 'Content-Type': 'application/json' } });
    }

    return new Response(JSON.stringify({ response: aiResponse.response }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
```

---

### 4. Google Calendar Connection
- Set up a Google Apps Script or Cloud Run endpoint to:
  - Accept POST requests with `calendar` and `message`
  - Authenticate with each account (<EMAIL>, <EMAIL>)
  - Use Google Calendar API to:
    - Find the next available slot
    - Create a calendar event
    - Email the confirmation + cancellation link to the user
- Store OAuth credentials per account separately

---

### 5. Embed Widget on elgatoai.com
```html
<div id="chatbox">
  <div id="messages"></div>
  <input id="msg" placeholder="Ask something..." />
  <button onclick="sendMsg()">Send</button>
</div>

<script>
function sendMsg() {
  const msg = document.getElementById('msg').value;
  fetch('https://elgatoai-bot.<your_subdomain>.workers.dev', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: msg,
      site: msg.includes('Mustafa') ? 'odmustafa' : 'elgatoai'
    })
  })
  .then(res => res.json())
  .then(data => {
    const box = document.getElementById('messages');
    box.innerHTML += '<div>' + data.response + '</div>';
    document.getElementById('msg').value = '';
  });
}
</script>
```

---

Now each chat interaction:
- Uses Cloudflare Workers AI to parse and reply
- Routes bookings <NAME_EMAIL> or <EMAIL>
- Can be embedded across multiple sites
- Sends brochures and calendar confirmations automatically
- All powered by serverless infrastructure with no backend hosting needed
