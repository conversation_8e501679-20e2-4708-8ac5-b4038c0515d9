#!/usr/bin/env node

/**
 * Test script for the ElgatoAI Booking Bot
 * This script tests the deployed worker functionality
 */

import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function testWorker(workerUrl) {
  console.log(`🧪 Testing worker at: ${workerUrl}\n`);
  
  const testCases = [
    {
      name: 'General Inquiry',
      payload: {
        message: 'What services do you offer?',
        site: 'elgatoai'
      }
    },
    {
      name: 'Booking Request - ElgatoAI',
      payload: {
        message: 'I would like to book a consultation about AI strategy',
        site: 'elgatoai',
        userEmail: '<EMAIL>'
      }
    },
    {
      name: 'Booking Request - Omar <PERSON>',
      payload: {
        message: 'I want to schedule a meeting with <PERSON> about technical consulting',
        site: 'odmustafa',
        userEmail: '<EMAIL>'
      }
    },
    {
      name: 'Brochure Request',
      payload: {
        message: 'Can you send me a brochure with your services?',
        site: 'elgatoai',
        userEmail: '<EMAIL>'
      }
    },
    {
      name: 'CORS Test (OPTIONS)',
      method: 'OPTIONS'
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n📋 Test: ${testCase.name}`);
    console.log('─'.repeat(50));
    
    try {
      const options = {
        method: testCase.method || 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'https://elgatoai.com'
        }
      };
      
      if (testCase.payload) {
        options.body = JSON.stringify(testCase.payload);
        console.log('Request:', JSON.stringify(testCase.payload, null, 2));
      }
      
      const response = await fetch(workerUrl, options);
      
      console.log(`Status: ${response.status} ${response.statusText}`);
      console.log('Headers:', Object.fromEntries(response.headers.entries()));
      
      if (response.headers.get('content-type')?.includes('application/json')) {
        const data = await response.json();
        console.log('Response:', JSON.stringify(data, null, 2));
      } else {
        const text = await response.text();
        console.log('Response:', text);
      }
      
      // Check for expected behaviors
      if (testCase.name.includes('Booking') && response.ok) {
        const data = await response.clone().json();
        if (data.response && data.response.includes('booking')) {
          console.log('✅ Booking logic triggered correctly');
        }
      }
      
      if (testCase.name.includes('CORS') && response.status === 200) {
        console.log('✅ CORS handling working');
      }
      
    } catch (error) {
      console.log('❌ Error:', error.message);
    }
  }
}

async function testCalendarEndpoint(calendarUrl) {
  console.log(`\n🗓️ Testing calendar endpoint: ${calendarUrl}\n`);
  
  const testBooking = {
    calendar: '<EMAIL>',
    message: 'Test booking from automated test',
    userEmail: '<EMAIL>',
    site: 'odmustafa'
  };
  
  try {
    console.log('Sending test booking request...');
    console.log('Payload:', JSON.stringify(testBooking, null, 2));
    
    const response = await fetch(calendarUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testBooking)
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    const data = await response.json();
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('✅ Calendar integration working');
    } else {
      console.log('❌ Calendar integration failed');
    }
    
  } catch (error) {
    console.log('❌ Calendar test error:', error.message);
  }
}

async function main() {
  console.log('🧪 ElgatoAI Booking Bot Test Suite\n');
  
  try {
    const workerUrl = await question('Enter your worker URL: ');
    
    if (!workerUrl) {
      console.log('❌ Worker URL is required');
      process.exit(1);
    }
    
    // Test the main worker
    await testWorker(workerUrl);
    
    // Test calendar endpoint if provided
    const testCalendar = await question('\nTest calendar endpoint? (y/n): ');
    
    if (testCalendar.toLowerCase() === 'y') {
      const calendarUrl = await question('Enter calendar API endpoint URL: ');
      if (calendarUrl) {
        await testCalendarEndpoint(calendarUrl);
      }
    }
    
    console.log('\n🎉 Testing complete!');
    console.log('\n📋 Summary:');
    console.log('- Test your worker with different message types');
    console.log('- Verify CORS headers are present');
    console.log('- Check that booking logic triggers correctly');
    console.log('- Ensure calendar integration works (if configured)');
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Embed the chat widget on your website');
    console.log('2. Test with real users');
    console.log('3. Monitor worker logs in Cloudflare dashboard');
    console.log('4. Customize AI prompts and responses as needed');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    rl.close();
  }
}

// Helper function to validate URL
function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

main();
