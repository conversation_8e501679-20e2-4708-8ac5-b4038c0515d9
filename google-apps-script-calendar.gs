/**
 * Google Apps Script for Calendar Integration
 * Deploy this as a web app to handle calendar bookings
 * 
 * Setup Instructions:
 * 1. Go to script.google.com
 * 2. Create a new project
 * 3. Paste this code
 * 4. Deploy as web app with execute permissions for "Anyone"
 * 5. Copy the web app URL to use as CALENDAR_API_ENDPOINT
 */

function doPost(e) {
  try {
    // Parse the incoming request
    const data = JSON.parse(e.postData.contents);
    const { calendar, message, userEmail, site } = data;
    
    console.log('Booking request received:', data);
    
    // Validate required fields
    if (!calendar || !message) {
      throw new Error('Missing required fields: calendar and message');
    }
    
    // Determine which calendar to use
    const calendarId = calendar === '<EMAIL>' ? 
      '<EMAIL>' : '<EMAIL>';
    
    // Get the calendar
    const cal = CalendarApp.getCalendarById(calendarId);
    
    if (!cal) {
      throw new Error(`Calendar not found: ${calendarId}`);
    }
    
    // Find next available business day slot
    const appointmentTime = findNextAvailableSlot(cal);
    
    // Create the calendar event
    const event = createCalendarEvent(cal, appointmentTime, site, message, userEmail);
    
    // Send confirmation email if user email provided
    if (userEmail && isValidEmail(userEmail)) {
      sendConfirmationEmail(userEmail, appointmentTime, site, calendar, event.getId());
    }
    
    // Send notification to calendar owner
    sendOwnerNotification(calendar, appointmentTime, message, userEmail, site);
    
    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        bookingId: event.getId(),
        dateTime: appointmentTime.toISOString(),
        message: 'Appointment scheduled successfully',
        calendarOwner: calendar
      }))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
      
  } catch (error) {
    console.error('Calendar booking error:', error);
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString(),
        message: 'Failed to schedule appointment'
      }))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
  }
}

/**
 * Handle OPTIONS requests for CORS
 */
function doOptions(e) {
  return ContentService
    .createTextOutput('')
    .setHeaders({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    });
}

/**
 * Find the next available time slot
 */
function findNextAvailableSlot(calendar) {
  const now = new Date();
  let appointmentTime = new Date();
  
  // Start from tomorrow at 2 PM
  appointmentTime.setDate(now.getDate() + 1);
  appointmentTime.setHours(14, 0, 0, 0);
  
  // Skip weekends
  while (appointmentTime.getDay() === 0 || appointmentTime.getDay() === 6) {
    appointmentTime.setDate(appointmentTime.getDate() + 1);
  }
  
  // Check for conflicts and find next available slot
  let attempts = 0;
  const maxAttempts = 14; // Check up to 2 weeks ahead
  
  while (attempts < maxAttempts) {
    const endTime = new Date(appointmentTime.getTime() + (60 * 60 * 1000)); // 1 hour meeting
    const events = calendar.getEvents(appointmentTime, endTime);
    
    if (events.length === 0) {
      // No conflicts, this slot is available
      break;
    }
    
    // Move to next hour
    appointmentTime.setHours(appointmentTime.getHours() + 1);
    
    // If past business hours (6 PM), move to next day at 9 AM
    if (appointmentTime.getHours() >= 18) {
      appointmentTime.setDate(appointmentTime.getDate() + 1);
      appointmentTime.setHours(9, 0, 0, 0);
      
      // Skip weekends
      while (appointmentTime.getDay() === 0 || appointmentTime.getDay() === 6) {
        appointmentTime.setDate(appointmentTime.getDate() + 1);
      }
    }
    
    attempts++;
  }
  
  return appointmentTime;
}

/**
 * Create a calendar event
 */
function createCalendarEvent(calendar, startTime, site, message, userEmail) {
  const endTime = new Date(startTime.getTime() + (60 * 60 * 1000)); // 1 hour meeting
  const title = `Consultation - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
  
  const description = `
Booking Request: ${message}

User Email: ${userEmail || 'Not provided'}
Site: ${site}
Booked via: ElgatoAI Chat Bot

Meeting Details:
- Duration: 1 hour
- Type: Consultation
- Status: Confirmed
  `.trim();
  
  const event = calendar.createEvent(title, startTime, endTime, {
    description: description,
    guests: userEmail ? [userEmail] : [],
    sendInvites: !!userEmail,
    location: 'Video Call (link will be provided)'
  });
  
  return event;
}

/**
 * Send confirmation email to user
 */
function sendConfirmationEmail(userEmail, appointmentTime, site, calendarOwner, eventId) {
  const subject = `Appointment Confirmed - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
  
  const body = `
Dear Client,

Thank you for booking a consultation with ${site === 'odmustafa' ? 'Omar Mustafa' : 'the ElgatoAI team'}!

Your appointment has been scheduled for:
📅 Date: ${appointmentTime.toDateString()}
🕐 Time: ${appointmentTime.toLocaleTimeString()}
⏱️ Duration: 1 hour
📧 Calendar: ${calendarOwner}

What to expect:
- You should receive a calendar invitation shortly
- We'll send you a video call link 15 minutes before the meeting
- Please prepare any questions or materials you'd like to discuss

Need to reschedule or cancel?
Simply reply to this email or contact us directly.

We look forward to speaking with you!

Best regards,
${site === 'odmustafa' ? 'Omar Mustafa' : 'The ElgatoAI Team'}

---
Booking ID: ${eventId}
Booked via: ElgatoAI Chat Assistant
  `.trim();
  
  try {
    GmailApp.sendEmail(userEmail, subject, body);
    console.log(`Confirmation email sent to: ${userEmail}`);
  } catch (error) {
    console.error('Failed to send confirmation email:', error);
  }
}

/**
 * Send notification to calendar owner
 */
function sendOwnerNotification(calendarOwner, appointmentTime, message, userEmail, site) {
  const subject = `New Booking: ${appointmentTime.toDateString()} at ${appointmentTime.toLocaleTimeString()}`;
  
  const body = `
New consultation booking received!

📅 Date: ${appointmentTime.toDateString()}
🕐 Time: ${appointmentTime.toLocaleTimeString()}
👤 Client Email: ${userEmail || 'Not provided'}
🌐 Site: ${site}

Original Message:
"${message}"

The appointment has been automatically added to your calendar.

${userEmail ? 'A confirmation email has been sent to the client.' : 'No client email provided - you may want to follow up.'}
  `.trim();
  
  try {
    GmailApp.sendEmail(calendarOwner, subject, body);
    console.log(`Owner notification sent to: ${calendarOwner}`);
  } catch (error) {
    console.error('Failed to send owner notification:', error);
  }
}

/**
 * Validate email address
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Test function - you can run this to test the booking system
 */
function testBooking() {
  const testData = {
    calendar: '<EMAIL>',
    message: 'I would like to book a consultation about AI strategy',
    userEmail: '<EMAIL>',
    site: 'odmustafa'
  };
  
  const mockEvent = {
    postData: {
      contents: JSON.stringify(testData)
    }
  };
  
  const result = doPost(mockEvent);
  console.log('Test result:', result.getContent());
}
