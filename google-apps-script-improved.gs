/**
 * IMPROVED Google Apps Script for Calendar Integration
 * This version handles POST requests more reliably
 * 
 * DEPLOYMENT STEPS:
 * 1. Replace your current script with this code
 * 2. Save (Ctrl+S)
 * 3. Deploy > New deployment (create a new version)
 * 4. Type: Web app
 * 5. Execute as: Me
 * 6. Who has access: Anyone
 * 7. Deploy and copy the new URL
 */

function doPost(e) {
  try {
    console.log('=== POST Request Received ===');
    console.log('Event object:', JSON.stringify(e, null, 2));
    
    // Handle different ways POST data might come in
    let data = {};
    
    if (e.postData) {
      console.log('Post data found:', e.postData.contents);
      
      if (e.postData.type === 'application/json') {
        data = JSON.parse(e.postData.contents);
      } else if (e.postData.type === 'application/x-www-form-urlencoded') {
        // Handle form data
        const params = new URLSearchParams(e.postData.contents);
        if (params.has('payload')) {
          data = JSON.parse(params.get('payload'));
        } else {
          // Convert form data to object
          for (const [key, value] of params) {
            data[key] = value;
          }
        }
      } else {
        // Try to parse as JSON anyway
        try {
          data = JSON.parse(e.postData.contents);
        } catch (parseError) {
          console.log('Failed to parse as JSON:', parseError);
          data = { rawData: e.postData.contents };
        }
      }
    }
    
    // Also check parameters (for GET-style parameters in POST)
    if (e.parameter) {
      console.log('Parameters found:', JSON.stringify(e.parameter));
      Object.assign(data, e.parameter);
    }
    
    console.log('Parsed data:', JSON.stringify(data));
    
    const { calendar, message, userEmail, site } = data;
    
    // Validate required fields
    if (!calendar || !message) {
      throw new Error('Missing required fields: calendar and message');
    }
    
    // For now, return test response (no actual calendar booking)
    const response = {
      success: true,
      message: 'Calendar endpoint is working! (Test mode - no actual booking created)',
      receivedData: {
        calendar: calendar,
        message: message,
        userEmail: userEmail || 'Not provided',
        site: site || 'Not specified'
      },
      timestamp: new Date().toISOString(),
      testMode: true,
      debug: {
        postDataType: e.postData ? e.postData.type : 'No post data',
        hasParameters: !!e.parameter,
        contentLength: e.postData ? e.postData.contents.length : 0
      }
    };
    
    console.log('Sending response:', JSON.stringify(response));
    
    const output = ContentService.createTextOutput(JSON.stringify(response));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
      
  } catch (error) {
    console.error('Error in doPost:', error);
    
    const errorResponse = {
      success: false,
      error: error.toString(),
      message: 'Failed to process request',
      timestamp: new Date().toISOString(),
      debug: {
        hasPostData: !!e.postData,
        hasParameters: !!e.parameter,
        postDataType: e.postData ? e.postData.type : 'None'
      }
    };
    
    const output = ContentService.createTextOutput(JSON.stringify(errorResponse));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
  }
}

/**
 * Handle GET requests (for testing)
 */
function doGet(e) {
  console.log('=== GET Request Received ===');
  console.log('Parameters:', JSON.stringify(e.parameter));
  
  const response = {
    message: 'ElgatoAI Calendar API is running',
    status: 'OK',
    timestamp: new Date().toISOString(),
    note: 'Send POST requests to this endpoint with calendar booking data',
    testEndpoint: 'Use POST with JSON: {"calendar": "<EMAIL>", "message": "test", "userEmail": "<EMAIL>", "site": "elgatoai"}',
    parameters: e.parameter || {}
  };
  
  const output = ContentService.createTextOutput(JSON.stringify(response));
  output.setMimeType(ContentService.MimeType.JSON);
  
  return output;
}

/**
 * Test function you can run manually
 */
function testEndpoint() {
  console.log('Running manual test...');
  
  const mockEvent = {
    postData: {
      contents: JSON.stringify({
        calendar: '<EMAIL>',
        message: 'Test message from manual test',
        userEmail: '<EMAIL>',
        site: 'test'
      }),
      type: 'application/json'
    }
  };
  
  const result = doPost(mockEvent);
  console.log('Test result:', result.getContent());
  
  return result.getContent();
}

/**
 * Test with form data
 */
function testFormData() {
  console.log('Testing form data...');
  
  const mockEvent = {
    postData: {
      contents: 'calendar=<EMAIL>&message=Test+form+message&userEmail=<EMAIL>&site=test',
      type: 'application/x-www-form-urlencoded'
    }
  };
  
  const result = doPost(mockEvent);
  console.log('Form test result:', result.getContent());
  
  return result.getContent();
}

/**
 * Simple hello function
 */
function hello() {
  console.log('Hello from Google Apps Script!');
  return 'Hello from Google Apps Script!';
}

/**
 * FULL CALENDAR INTEGRATION VERSION
 * Replace doPost with this function once the basic version works
 */
function doPostWithCalendar(e) {
  try {
    // Parse data (same logic as above)
    let data = {};
    if (e.postData) {
      if (e.postData.type === 'application/json') {
        data = JSON.parse(e.postData.contents);
      } else {
        try {
          data = JSON.parse(e.postData.contents);
        } catch (parseError) {
          data = { rawData: e.postData.contents };
        }
      }
    }
    
    const { calendar, message, userEmail, site } = data;
    
    if (!calendar || !message) {
      throw new Error('Missing required fields: calendar and message');
    }
    
    // Determine which calendar to use
    const calendarId = calendar === '<EMAIL>' ? 
      '<EMAIL>' : '<EMAIL>';
    
    // Get the calendar
    const cal = CalendarApp.getCalendarById(calendarId);
    
    if (!cal) {
      throw new Error(`Calendar not found: ${calendarId}`);
    }
    
    // Find next available slot
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0); // 2 PM tomorrow
    
    // Skip weekends
    while (tomorrow.getDay() === 0 || tomorrow.getDay() === 6) {
      tomorrow.setDate(tomorrow.getDate() + 1);
    }
    
    const endTime = new Date(tomorrow.getTime() + (60 * 60 * 1000)); // 1 hour later
    
    // Create calendar event
    const title = `Consultation - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
    const description = `Booking Request: ${message}\n\nUser Email: ${userEmail || 'Not provided'}\nSite: ${site}\nBooked via: ElgatoAI Chat Bot`;
    
    const event = cal.createEvent(title, tomorrow, endTime, {
      description: description,
      guests: userEmail ? [userEmail] : [],
      sendInvites: !!userEmail,
      location: 'Video Call (link will be provided)'
    });
    
    // Send confirmation email if user email provided
    if (userEmail && isValidEmail(userEmail)) {
      const subject = `Appointment Confirmed - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
      const body = `Dear Client,

Thank you for booking a consultation!

Your appointment has been scheduled for:
📅 Date: ${tomorrow.toDateString()}
🕐 Time: ${tomorrow.toLocaleTimeString()}
⏱️ Duration: 1 hour

We look forward to speaking with you!

Best regards,
${site === 'odmustafa' ? 'Omar Mustafa' : 'The ElgatoAI Team'}`;
      
      GmailApp.sendEmail(userEmail, subject, body);
    }
    
    const response = {
      success: true,
      bookingId: event.getId(),
      dateTime: tomorrow.toISOString(),
      message: 'Appointment scheduled successfully',
      calendarOwner: calendar
    };
    
    const output = ContentService.createTextOutput(JSON.stringify(response));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
      
  } catch (error) {
    const errorResponse = {
      success: false,
      error: error.toString(),
      message: 'Failed to schedule appointment'
    };
    
    const output = ContentService.createTextOutput(JSON.stringify(errorResponse));
    output.setMimeType(ContentService.MimeType.JSON);
    
    return output;
  }
}

/**
 * Validate email address
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
