/**
 * SIMPL<PERSON>IED Google Apps Script for Calendar Integration
 * Use this version first to test basic functionality
 * 
 * DEPLOYMENT STEPS:
 * 1. Go to script.google.com
 * 2. Create new project
 * 3. Paste this code
 * 4. Save (Ctrl+S)
 * 5. Deploy > New deployment
 * 6. Type: Web app
 * 7. Execute as: Me
 * 8. Who has access: Anyone
 * 9. Deploy
 * 10. Grant all permissions when prompted
 */

function doPost(e) {
  try {
    // Log the incoming request for debugging
    console.log('Received POST request');
    console.log('Request data:', e.postData ? e.postData.contents : 'No data');
    
    // Parse the request data
    let data = {};
    if (e.postData && e.postData.contents) {
      data = JSON.parse(e.postData.contents);
    }
    
    const { calendar, message, userEmail, site } = data;
    
    // Validate required fields
    if (!calendar || !message) {
      throw new Error('Missing required fields: calendar and message');
    }
    
    // For now, just return a success response without actually creating calendar events
    // This helps us test the basic deployment first
    const response = {
      success: true,
      message: 'Calendar endpoint is working! (Test mode - no actual booking created)',
      receivedData: {
        calendar: calendar,
        message: message,
        userEmail: userEmail || 'Not provided',
        site: site || 'Not specified'
      },
      timestamp: new Date().toISOString(),
      testMode: true
    };
    
    console.log('Sending response:', JSON.stringify(response));
    
    return ContentService
      .createTextOutput(JSON.stringify(response))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
      
  } catch (error) {
    console.error('Error in doPost:', error);
    
    const errorResponse = {
      success: false,
      error: error.toString(),
      message: 'Failed to process request',
      timestamp: new Date().toISOString()
    };
    
    return ContentService
      .createTextOutput(JSON.stringify(errorResponse))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
  }
}

/**
 * Handle OPTIONS requests for CORS
 */
function doOptions(e) {
  console.log('Received OPTIONS request');
  
  return ContentService
    .createTextOutput('')
    .setHeaders({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    });
}

/**
 * Test function you can run manually in the Apps Script editor
 */
function testEndpoint() {
  console.log('Running manual test...');
  
  const mockEvent = {
    postData: {
      contents: JSON.stringify({
        calendar: '<EMAIL>',
        message: 'Test message from manual test',
        userEmail: '<EMAIL>',
        site: 'test'
      })
    }
  };
  
  const result = doPost(mockEvent);
  console.log('Test result:', result.getContent());
  
  return result.getContent();
}

/**
 * Simple function to verify the script is working
 */
function hello() {
  console.log('Hello from Google Apps Script!');
  return 'Hello from Google Apps Script!';
}

/**
 * FULL VERSION - Use this after the simple version works
 * Uncomment and modify the doPost function above to use this code
 */
/*
function doPostFull(e) {
  try {
    const data = JSON.parse(e.postData.contents);
    const { calendar, message, userEmail, site } = data;
    
    if (!calendar || !message) {
      throw new Error('Missing required fields: calendar and message');
    }
    
    // Determine which calendar to use
    const calendarId = calendar === '<EMAIL>' ? 
      '<EMAIL>' : '<EMAIL>';
    
    // Get the calendar
    const cal = CalendarApp.getCalendarById(calendarId);
    
    if (!cal) {
      throw new Error(`Calendar not found: ${calendarId}`);
    }
    
    // Find next available slot (simplified)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0); // 2 PM tomorrow
    
    // Skip weekends
    while (tomorrow.getDay() === 0 || tomorrow.getDay() === 6) {
      tomorrow.setDate(tomorrow.getDate() + 1);
    }
    
    const endTime = new Date(tomorrow.getTime() + (60 * 60 * 1000)); // 1 hour later
    
    // Create calendar event
    const title = `Consultation - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
    const description = `Booking Request: ${message}\n\nUser Email: ${userEmail || 'Not provided'}\nSite: ${site}\nBooked via: ElgatoAI Chat Bot`;
    
    const event = cal.createEvent(title, tomorrow, endTime, {
      description: description,
      guests: userEmail ? [userEmail] : [],
      sendInvites: !!userEmail,
      location: 'Video Call (link will be provided)'
    });
    
    // Send confirmation email if user email provided
    if (userEmail) {
      const subject = `Appointment Confirmed - ${site === 'odmustafa' ? 'Omar Mustafa' : 'ElgatoAI'}`;
      const body = `Your appointment has been scheduled for ${tomorrow.toDateString()} at ${tomorrow.toLocaleTimeString()}`;
      GmailApp.sendEmail(userEmail, subject, body);
    }
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        bookingId: event.getId(),
        dateTime: tomorrow.toISOString(),
        message: 'Appointment scheduled successfully',
        calendarOwner: calendar
      }))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
      
  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString(),
        message: 'Failed to schedule appointment'
      }))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
  }
}
*/
